/**
 * Portal Application JavaScript
 * Client-side functionality and security enhancements
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize application
    initializeApp();
});

/**
 * Initialize application
 */
function initializeApp() {
    // Auto-hide alerts after 5 seconds
    autoHideAlerts();
    
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize session timeout warning
    initializeSessionTimeout();
    
    // Initialize CSRF token refresh
    initializeCSRFRefresh();
}

/**
 * Auto-hide alert messages
 */
function autoHideAlerts() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    });
}

/**
 * Validate form
 */
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required]');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Validate individual field
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Remove existing error
    removeFieldError(field);
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        errorMessage = 'This field is required.';
        isValid = false;
    }
    
    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            errorMessage = 'Please enter a valid email address.';
            isValid = false;
        }
    }
    
    // Password validation
    if (field.type === 'password' && value && field.name === 'password') {
        if (value.length < 6) {
            errorMessage = 'Password must be at least 6 characters long.';
            isValid = false;
        }
    }
    
    // Show error if validation failed
    if (!isValid) {
        showFieldError(field, errorMessage);
    }
    
    return isValid;
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    field.classList.add('error');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * Remove field error
 */
function removeFieldError(field) {
    field.classList.remove('error');
    
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

/**
 * Initialize session timeout warning
 */
function initializeSessionTimeout() {
    // Check session every 5 minutes
    setInterval(checkSession, 5 * 60 * 1000);
    
    // Warn user 5 minutes before session expires
    setTimeout(showSessionWarning, 55 * 60 * 1000); // 55 minutes
}

/**
 * Check session status
 */
function checkSession() {
    fetch('api/check_session.php')
        .then(response => response.json())
        .then(data => {
            if (!data.valid) {
                showAlert('Your session has expired. Please log in again.', 'error');
                setTimeout(() => {
                    window.location.href = 'login.php';
                }, 3000);
            }
        })
        .catch(error => {
            console.error('Session check failed:', error);
        });
}

/**
 * Show session timeout warning
 */
function showSessionWarning() {
    if (confirm('Your session will expire in 5 minutes. Click OK to extend your session.')) {
        // Refresh session by making a request
        fetch('api/refresh_session.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Session extended successfully.', 'success');
                    // Reset the warning timer
                    setTimeout(showSessionWarning, 55 * 60 * 1000);
                }
            });
    }
}

/**
 * Initialize CSRF token refresh
 */
function initializeCSRFRefresh() {
    // Refresh CSRF token every 30 minutes
    setInterval(refreshCSRFToken, 30 * 60 * 1000);
}

/**
 * Refresh CSRF token
 */
function refreshCSRFToken() {
    fetch('api/get_csrf_token.php')
        .then(response => response.json())
        .then(data => {
            if (data.token) {
                // Update all CSRF token inputs
                const csrfInputs = document.querySelectorAll('input[name="csrf_token"]');
                csrfInputs.forEach(input => {
                    input.value = data.token;
                });
            }
        })
        .catch(error => {
            console.error('CSRF token refresh failed:', error);
        });
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    // Insert at the top of the main content
    const mainContent = document.querySelector('.main-content') || document.body;
    mainContent.insertBefore(alertDiv, mainContent.firstChild);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        alertDiv.style.opacity = '0';
        setTimeout(() => {
            alertDiv.remove();
        }, 300);
    }, 5000);
}

/**
 * Confirm action
 */
function confirmAction(message) {
    return confirm(message);
}

/**
 * Format currency
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-ZA', {
        style: 'currency',
        currency: 'ZAR'
    }).format(amount);
}

/**
 * Format date
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA');
}

/**
 * Sanitize HTML
 */
function sanitizeHTML(str) {
    const temp = document.createElement('div');
    temp.textContent = str;
    return temp.innerHTML;
}

// Add CSS for field errors
const style = document.createElement('style');
style.textContent = `
    .field-error {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    input.error {
        border-color: #dc3545 !important;
    }
`;
document.head.appendChild(style);
