<?php
/**
 * Activity Logs Viewer
 * Master Admin interface for viewing all system activity
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

// Get filter parameters
$action_filter = $_GET['action'] ?? '';
$user_filter = $_GET['user'] ?? '';
$company_filter = $_GET['company'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 50;
$offset = ($page - 1) * $per_page;

// Build query
$query = "SELECT al.*, u.name as user_name, u.email as user_email, c.company_name, c.company_code 
          FROM activity_logs al 
          LEFT JOIN users u ON al.user_id = u.id 
          LEFT JOIN companies c ON al.company_id = c.id 
          WHERE 1=1";

$params = [];

if ($action_filter) {
    $query .= " AND al.action_type = ?";
    $params[] = $action_filter;
}

if ($user_filter) {
    $query .= " AND al.user_id = ?";
    $params[] = $user_filter;
}

if ($company_filter) {
    $query .= " AND al.company_id = ?";
    $params[] = $company_filter;
}

if ($date_from) {
    $query .= " AND DATE(al.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $query .= " AND DATE(al.created_at) <= ?";
    $params[] = $date_to;
}

// Count total records
$count_query = str_replace("SELECT al.*, u.name as user_name, u.email as user_email, c.company_name, c.company_code", "SELECT COUNT(*)", $query);
$count_stmt = $db->prepare($count_query);
$count_stmt->execute($params);
$total_records = $count_stmt->fetchColumn();
$total_pages = ceil($total_records / $per_page);

// Get paginated results
$query .= " ORDER BY al.created_at DESC LIMIT ? OFFSET ?";
$params[] = $per_page;
$params[] = $offset;

$stmt = $db->prepare($query);
$stmt->execute($params);
$activities = $stmt->fetchAll();

// Get filter options
$action_types = [
    'login' => 'Login',
    'logout' => 'Logout',
    'company_created' => 'Company Created',
    'user_created' => 'User Created',
    'user_updated' => 'User Updated',
    'user_deleted' => 'User Deleted',
    'payment_made' => 'Payment Made',
    'payment_failed' => 'Payment Failed',
    'invoice_downloaded' => 'Invoice Downloaded',
    'permission_changed' => 'Permission Changed',
    'system_error' => 'System Error'
];

// Get users for filter
$user_query = "SELECT id, name, email FROM users WHERE status = 'active' ORDER BY name";
$user_stmt = $db->prepare($user_query);
$user_stmt->execute();
$users = $user_stmt->fetchAll();

// Get companies for filter
$company_query = "SELECT id, company_name FROM companies WHERE status = 'active' ORDER BY company_name";
$company_stmt = $db->prepare($company_query);
$company_stmt->execute();
$companies = $company_stmt->fetchAll();

$page_title = "Activity Logs";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .filter-label {
            font-weight: 600;
            color: #2B5E5F;
            font-size: 0.9rem;
        }
        
        .filter-input, .filter-select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }
        
        .activity-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .activity-table th {
            background: #2B5E5F;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .activity-table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: top;
        }
        
        .activity-table tr:hover {
            background: #f8fafa;
        }
        
        .action-icon {
            font-size: 1.2rem;
            margin-right: 0.5rem;
        }
        
        .action-type {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .action-login { background: #d1fae5; color: #065f46; }
        .action-logout { background: #fee2e2; color: #991b1b; }
        .action-company_created { background: #dbeafe; color: #1e40af; }
        .action-user_created { background: #fef3c7; color: #92400e; }
        .action-user_updated { background: #e0e7ff; color: #3730a3; }
        .action-user_deleted { background: #fee2e2; color: #991b1b; }
        .action-payment_made { background: #d1fae5; color: #065f46; }
        .action-payment_failed { background: #fee2e2; color: #991b1b; }
        .action-invoice_downloaded { background: #f3f4f6; color: #374151; }
        .action-permission_changed { background: #fef3c7; color: #92400e; }
        .action-system_error { background: #fee2e2; color: #991b1b; }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .pagination a, .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            text-decoration: none;
            color: #374151;
        }
        
        .pagination a:hover {
            background: #f9fafb;
        }
        
        .pagination .current {
            background: #2B5E5F;
            color: white;
            border-color: #2B5E5F;
        }
        
        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2B5E5F;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'activity.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Activity Logs</h1>
            <p class="page-subtitle">Monitor all system activity and user actions</p>
        </div>
        
        <!-- Summary Stats -->
        <div class="stats-summary">
            <div class="stat-item">
                <div class="stat-number"><?php echo number_format($total_records); ?></div>
                <div class="stat-label">Total Activities</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo $total_pages; ?></div>
                <div class="stat-label">Total Pages</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo $page; ?></div>
                <div class="stat-label">Current Page</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo count($activities); ?></div>
                <div class="stat-label">Showing Records</div>
            </div>
        </div>
        
        <div class="admin-card">
            <!-- Filters -->
            <form method="GET" class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Action Type</label>
                    <select name="action" class="filter-select">
                        <option value="">All Actions</option>
                        <?php foreach ($action_types as $value => $label): ?>
                            <option value="<?php echo $value; ?>" <?php echo $action_filter === $value ? 'selected' : ''; ?>>
                                <?php echo $label; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">User</label>
                    <select name="user" class="filter-select">
                        <option value="">All Users</option>
                        <?php foreach ($users as $user_item): ?>
                            <option value="<?php echo $user_item['id']; ?>" <?php echo $user_filter == $user_item['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user_item['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Company</label>
                    <select name="company" class="filter-select">
                        <option value="">All Companies</option>
                        <?php foreach ($companies as $company): ?>
                            <option value="<?php echo $company['id']; ?>" <?php echo $company_filter == $company['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($company['company_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Date From</label>
                    <input type="date" name="date_from" class="filter-input" value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Date To</label>
                    <input type="date" name="date_to" class="filter-input" value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                
                <div class="filter-group">
                    <button type="submit" class="btn">Filter</button>
                    <a href="activity.php" class="btn btn-secondary">Clear</a>
                </div>
            </form>
            
            <!-- Activity Table -->
            <table class="activity-table">
                <thead>
                    <tr>
                        <th>Action</th>
                        <th>Description</th>
                        <th>User</th>
                        <th>Company</th>
                        <th>IP Address</th>
                        <th>Date/Time</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($activities as $activity): ?>
                        <tr>
                            <td>
                                <span class="action-type action-<?php echo $activity['action_type']; ?>">
                                    <?php
                                    $action_icons = [
                                        'login' => '🔐',
                                        'logout' => '🚪',
                                        'company_created' => '🏢',
                                        'user_created' => '👤',
                                        'user_updated' => '✏️',
                                        'user_deleted' => '🗑️',
                                        'payment_made' => '💳',
                                        'payment_failed' => '❌',
                                        'invoice_downloaded' => '📥',
                                        'permission_changed' => '🔑',
                                        'system_error' => '⚠️'
                                    ];
                                    echo $action_icons[$activity['action_type']] ?? '📝';
                                    ?>
                                    <?php echo $action_types[$activity['action_type']] ?? ucfirst(str_replace('_', ' ', $activity['action_type'])); ?>
                                </span>
                            </td>
                            <td>
                                <div style="font-weight: 600; margin-bottom: 0.25rem;">
                                    <?php echo htmlspecialchars($activity['description']); ?>
                                </div>
                                <?php if ($activity['metadata']): ?>
                                    <div style="font-size: 0.8rem; color: #666;">
                                        <code><?php echo htmlspecialchars($activity['metadata']); ?></code>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($activity['user_name']): ?>
                                    <div style="font-weight: 600;"><?php echo htmlspecialchars($activity['user_name']); ?></div>
                                    <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($activity['user_email']); ?></div>
                                <?php else: ?>
                                    <span style="color: #999;">System</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($activity['company_name']): ?>
                                    <div><?php echo htmlspecialchars($activity['company_name']); ?></div>
                                    <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($activity['company_code']); ?></div>
                                <?php else: ?>
                                    <span style="color: #999;">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <code style="font-size: 0.85rem;"><?php echo htmlspecialchars($activity['ip_address'] ?? 'N/A'); ?></code>
                            </td>
                            <td>
                                <div><?php echo date('M j, Y', strtotime($activity['created_at'])); ?></div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo date('H:i:s', strtotime($activity['created_at'])); ?></div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if (empty($activities)): ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <p>No activity logs found for the selected criteria.</p>
                </div>
            <?php endif; ?>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">Previous</a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <?php if ($i == $page): ?>
                            <span class="current"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">Next</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>
</body>
</html>
