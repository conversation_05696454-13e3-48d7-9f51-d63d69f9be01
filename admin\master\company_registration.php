<?php
/**
 * Company Registration Form
 * Master Admin interface for registering new companies
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        // Get form data
        $company_name = trim($_POST['company_name']);
        $address = trim($_POST['address']);
        $telephone = trim($_POST['telephone']);
        $fax = trim($_POST['fax']);
        $email = trim($_POST['email']);
        $website = trim($_POST['website']);
        $company_reg_no = trim($_POST['company_reg_no']);
        $vat_no = trim($_POST['vat_no']);
        $customs_code = trim($_POST['customs_code']);
        $debtor_account_number = trim($_POST['debtor_account_number']);
        
        // Validate required fields
        if (empty($company_name) || empty($email)) {
            $error_message = 'Company name and email are required.';
        } else {
            try {
                // Generate unique company code
                $company_code = generateCompanyCode($company_name, $db);
                
                // Generate FTP folder path
                $ftp_folder_path = '/invoices/' . strtolower($company_code) . '/';
                
                // Insert company into database
                $query = "INSERT INTO companies (company_name, company_code, address, telephone, fax, email, website, 
                                               company_reg_no, vat_no, customs_code, debtor_account_number, ftp_folder_path) 
                          VALUES (:company_name, :company_code, :address, :telephone, :fax, :email, :website, 
                                  :company_reg_no, :vat_no, :customs_code, :debtor_account_number, :ftp_folder_path)";
                
                $stmt = $db->prepare($query);
                $stmt->bindParam(':company_name', $company_name);
                $stmt->bindParam(':company_code', $company_code);
                $stmt->bindParam(':address', $address);
                $stmt->bindParam(':telephone', $telephone);
                $stmt->bindParam(':fax', $fax);
                $stmt->bindParam(':email', $email);
                $stmt->bindParam(':website', $website);
                $stmt->bindParam(':company_reg_no', $company_reg_no);
                $stmt->bindParam(':vat_no', $vat_no);
                $stmt->bindParam(':customs_code', $customs_code);
                $stmt->bindParam(':debtor_account_number', $debtor_account_number);
                $stmt->bindParam(':ftp_folder_path', $ftp_folder_path);
                
                if ($stmt->execute()) {
                    $company_id = $db->lastInsertId();
                    
                    // Log activity
                    logActivity('company_created', "New company registered: {$company_name} ({$company_code})", $_SESSION['user_id'], $company_id);
                    
                    $success_message = "Company '{$company_name}' has been successfully registered with code: {$company_code}";
                    
                    // Clear form data
                    $_POST = [];
                } else {
                    $error_message = 'Failed to register company. Please try again.';
                }
                
            } catch (PDOException $e) {
                if ($e->getCode() == 23000) {
                    $error_message = 'A company with this email already exists.';
                } else {
                    $error_message = 'Database error occurred. Please try again.';
                }
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generateCsrfToken();

$page_title = "Register New Company";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2B5E5F;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #2B5E5F;
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'company_registration.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Register New Company</h1>
            <p class="page-subtitle">Add a new company to the portal system</p>
        </div>
        
        <div class="admin-card">
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="company_registration.php">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                <div class="form-grid">
                    <div class="form-group">
                        <label for="company_name" class="form-label">Company Name *</label>
                        <input type="text" id="company_name" name="company_name" class="form-input" required
                               value="<?php echo isset($_POST['company_name']) ? htmlspecialchars($_POST['company_name']) : ''; ?>"
                               placeholder="Enter company name">
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email Address *</label>
                        <input type="email" id="email" name="email" class="form-input" required
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                               placeholder="Enter email address">
                    </div>

                    <div class="form-group">
                        <label for="telephone" class="form-label">Telephone</label>
                        <input type="tel" id="telephone" name="telephone" class="form-input"
                               value="<?php echo isset($_POST['telephone']) ? htmlspecialchars($_POST['telephone']) : ''; ?>"
                               placeholder="Enter telephone number">
                    </div>

                    <div class="form-group">
                        <label for="fax" class="form-label">Fax</label>
                        <input type="tel" id="fax" name="fax" class="form-input"
                               value="<?php echo isset($_POST['fax']) ? htmlspecialchars($_POST['fax']) : ''; ?>"
                               placeholder="Enter fax number">
                    </div>

                    <div class="form-group">
                        <label for="website" class="form-label">Website</label>
                        <input type="url" id="website" name="website" class="form-input"
                               value="<?php echo isset($_POST['website']) ? htmlspecialchars($_POST['website']) : ''; ?>"
                               placeholder="Enter website URL">
                    </div>

                    <div class="form-group">
                        <label for="company_reg_no" class="form-label">Company Registration Number</label>
                        <input type="text" id="company_reg_no" name="company_reg_no" class="form-input"
                               value="<?php echo isset($_POST['company_reg_no']) ? htmlspecialchars($_POST['company_reg_no']) : ''; ?>"
                               placeholder="Enter registration number">
                    </div>

                    <div class="form-group">
                        <label for="vat_no" class="form-label">VAT Number</label>
                        <input type="text" id="vat_no" name="vat_no" class="form-input"
                               value="<?php echo isset($_POST['vat_no']) ? htmlspecialchars($_POST['vat_no']) : ''; ?>"
                               placeholder="Enter VAT number">
                    </div>

                    <div class="form-group">
                        <label for="customs_code" class="form-label">Customs Code</label>
                        <input type="text" id="customs_code" name="customs_code" class="form-input"
                               value="<?php echo isset($_POST['customs_code']) ? htmlspecialchars($_POST['customs_code']) : ''; ?>"
                               placeholder="Enter customs code">
                    </div>

                    <div class="form-group">
                        <label for="debtor_account_number" class="form-label">Debtor Account Number</label>
                        <input type="text" id="debtor_account_number" name="debtor_account_number" class="form-input"
                               value="<?php echo isset($_POST['debtor_account_number']) ? htmlspecialchars($_POST['debtor_account_number']) : ''; ?>"
                               placeholder="Enter debtor account number">
                    </div>

                    <div class="form-group full-width">
                        <label for="address" class="form-label">Address</label>
                        <textarea id="address" name="address" class="form-input form-textarea"
                                  placeholder="Enter company address"><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                    </div>
                </div>

                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <a href="dashboard.php" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn">Register Company</button>
                </div>
            </form>
        </div>
    </main>
</body>
</html>
