<?php
/**
 * User Profile Management
 * Allows users to view and update their profile information
 */

require_once 'config/config.php';
require_once 'config/database.php';

// Require login
requireLogin();

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_profile') {
            $name = trim($_POST['name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            
            if (empty($name)) {
                $error_message = 'Name is required.';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error_message = 'Please enter a valid email address.';
            } else {
                // Check if email is already taken by another user
                $check_query = "SELECT id FROM users WHERE email = ? AND id != ?";
                $check_stmt = $db->prepare($check_query);
                $check_stmt->execute([$email, $_SESSION['user_id']]);
                
                if ($check_stmt->fetchColumn()) {
                    $error_message = 'This email address is already in use by another user.';
                } else {
                    $query = "UPDATE users SET name = ?, email = ?, phone = ?, updated_at = NOW() WHERE id = ?";
                    $stmt = $db->prepare($query);
                    
                    if ($stmt->execute([$name, $email, $phone, $_SESSION['user_id']])) {
                        $_SESSION['user_name'] = $name;
                        $_SESSION['user_email'] = $email;
                        $success_message = 'Profile updated successfully.';
                        logActivity('profile_updated', 'User profile information updated', $_SESSION['user_id'], $_SESSION['company_id']);
                    } else {
                        $error_message = 'Failed to update profile. Please try again.';
                    }
                }
            }
        } elseif ($action === 'change_password') {
            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                $error_message = 'All password fields are required.';
            } elseif ($new_password !== $confirm_password) {
                $error_message = 'New passwords do not match.';
            } elseif (strlen($new_password) < 6) {
                $error_message = 'New password must be at least 6 characters long.';
            } else {
                // Verify current password
                $query = "SELECT password FROM users WHERE id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$_SESSION['user_id']]);
                $stored_password = $stmt->fetchColumn();
                
                if (!password_verify($current_password, $stored_password)) {
                    $error_message = 'Current password is incorrect.';
                } else {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $update_query = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
                    $update_stmt = $db->prepare($update_query);
                    
                    if ($update_stmt->execute([$hashed_password, $_SESSION['user_id']])) {
                        $success_message = 'Password changed successfully.';
                        logActivity('password_changed', 'User password changed', $_SESSION['user_id'], $_SESSION['company_id']);
                    } else {
                        $error_message = 'Failed to change password. Please try again.';
                    }
                }
            }
        }
    }
}

// Get current user information
$query = "SELECT u.*, c.company_name, c.company_code 
          FROM users u 
          LEFT JOIN companies c ON u.company_id = c.id 
          WHERE u.id = ?";
$stmt = $db->prepare($query);
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: logout.php');
    exit();
}

$csrf_token = generateCsrfToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .profile-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .profile-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .profile-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2B5E5F;
            margin-bottom: 0.5rem;
        }
        
        .profile-subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        
        .profile-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0;
        }
        
        .tab-button {
            padding: 1rem 2rem;
            background: none;
            border: none;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            color: #2B5E5F;
            border-bottom-color: #2B5E5F;
        }
        
        .tab-button:hover {
            color: #2B5E5F;
            background: #f8fafa;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .profile-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
            margin-bottom: 2rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #2B5E5F;
            margin-bottom: 0.5rem;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #2B5E5F;
        }
        
        .form-input:disabled {
            background: #f9fafb;
            color: #666;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #2B5E5F;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1a4344;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
        }
        
        .info-box {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .info-box-title {
            font-weight: 600;
            color: #0c4a6e;
            margin-bottom: 0.5rem;
        }
        
        .info-box-text {
            color: #0c4a6e;
            font-size: 0.9rem;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #2B5E5F;
            text-decoration: none;
            font-weight: 600;
            margin-bottom: 2rem;
        }
        
        .back-link:hover {
            color: #1a4344;
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <a href="dashboard.php" class="back-link">
            ← Back to Dashboard
        </a>
        
        <div class="profile-header">
            <h1 class="profile-title">My Profile</h1>
            <p class="profile-subtitle">Manage your account information and settings</p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Profile Tabs -->
        <div class="profile-tabs">
            <button class="tab-button active" onclick="showTab('profile-info')">Profile Information</button>
            <button class="tab-button" onclick="showTab('change-password')">Change Password</button>
            <button class="tab-button" onclick="showTab('account-info')">Account Details</button>
        </div>
        
        <!-- Profile Information Tab -->
        <div id="profile-info" class="tab-content active">
            <div class="profile-card">
                <h3 style="color: #2B5E5F; margin-bottom: 1.5rem;">Profile Information</h3>
                
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">Full Name *</label>
                            <input type="text" name="name" class="form-input" 
                                   value="<?php echo htmlspecialchars($user['name']); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Email Address *</label>
                            <input type="email" name="email" class="form-input" 
                                   value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Phone Number</label>
                            <input type="tel" name="phone" class="form-input" 
                                   value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Update Profile</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Change Password Tab -->
        <div id="change-password" class="tab-content">
            <div class="profile-card">
                <h3 style="color: #2B5E5F; margin-bottom: 1.5rem;">Change Password</h3>
                
                <div class="info-box">
                    <div class="info-box-title">Password Requirements</div>
                    <div class="info-box-text">
                        Your password must be at least 6 characters long. For better security, use a combination of letters, numbers, and special characters.
                    </div>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="form-group">
                        <label class="form-label">Current Password *</label>
                        <input type="password" name="current_password" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">New Password *</label>
                        <input type="password" name="new_password" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Confirm New Password *</label>
                        <input type="password" name="confirm_password" class="form-input" required>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Change Password</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Account Details Tab -->
        <div id="account-info" class="tab-content">
            <div class="profile-card">
                <h3 style="color: #2B5E5F; margin-bottom: 1.5rem;">Account Details</h3>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">User ID</label>
                        <input type="text" class="form-input" value="<?php echo htmlspecialchars($user['id']); ?>" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Role</label>
                        <input type="text" class="form-input" value="<?php echo ucfirst(str_replace('_', ' ', $user['role'])); ?>" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Company</label>
                        <input type="text" class="form-input" value="<?php echo htmlspecialchars($user['company_name'] ?? 'N/A'); ?>" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Company Code</label>
                        <input type="text" class="form-input" value="<?php echo htmlspecialchars($user['company_code'] ?? 'N/A'); ?>" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Account Status</label>
                        <input type="text" class="form-input" value="<?php echo ucfirst($user['status']); ?>" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Member Since</label>
                        <input type="text" class="form-input" value="<?php echo date('F j, Y', strtotime($user['created_at'])); ?>" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Last Login</label>
                        <input type="text" class="form-input" 
                               value="<?php echo $user['last_login'] ? date('F j, Y g:i A', strtotime($user['last_login'])) : 'Never'; ?>" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Last Updated</label>
                        <input type="text" class="form-input" 
                               value="<?php echo $user['updated_at'] ? date('F j, Y g:i A', strtotime($user['updated_at'])) : 'Never'; ?>" disabled>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
