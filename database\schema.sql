-- ============================================================================
-- MULTI-TENANT PORTAL APPLICATION DATABASE SCHEMA
-- ============================================================================
-- Compatible with phpMyAdmin and MySQL 5.7+
--
-- INSTRUCTIONS FOR PHPMYADMIN:
-- 1. Create a new database called 'portal_app' if it doesn't exist
-- 2. Select the 'portal_app' database
-- 3. Copy and paste this entire SQL script into the SQL tab
-- 4. Click 'Go' to execute
--
-- ALTERNATIVE: If you want to create the database automatically:
-- 1. Uncomment the lines below and run them first
-- 2. Then run the rest of the script
--
-- CREATE DATABASE IF NOT EXISTS portal_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE portal_app;
-- ============================================================================

-- Drop tables if they exist (for clean reinstall)
DROP TABLE IF EXISTS activity_logs;
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS user_rights;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS companies;

-- Companies table (tenants) - Enhanced for multi-tenant architecture
CREATE TABLE companies (
    id INT(11) NOT NULL AUTO_INCREMENT,
    company_name VARCHAR(255) NOT NULL,
    company_code VARCHAR(50) NOT NULL,
    address TEXT,
    telephone VARCHAR(50),
    fax VARCHAR(50),
    email VARCHAR(255),
    website VARCHAR(255),
    company_reg_no VARCHAR(100),
    vat_no VARCHAR(100),
    customs_code VARCHAR(100),
    debtor_account_number VARCHAR(100),
    ftp_folder_path VARCHAR(500) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'suspended', 'inactive') NOT NULL DEFAULT 'active',
    PRIMARY KEY (id),
    UNIQUE KEY company_code (company_code),
    KEY idx_company_code (company_code),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Users table - Enhanced for multi-tenant architecture
CREATE TABLE users (
    id INT(11) NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('master_admin', 'company_admin', 'user') NOT NULL DEFAULT 'user',
    company_id INT(11) DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL DEFAULT NULL,
    status ENUM('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
    created_by INT(11) DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY unique_email_company (email, company_id),
    KEY idx_email (email),
    KEY idx_company_id (company_id),
    KEY idx_role (role),
    KEY idx_status (status),
    KEY fk_users_created_by (created_by),
    CONSTRAINT fk_users_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    CONSTRAINT fk_users_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User rights table
CREATE TABLE user_rights (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) NOT NULL,
    module_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_user_module (user_id, module_name),
    KEY idx_user_id (user_id),
    CONSTRAINT fk_user_rights_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Payments table - Enhanced for multi-tenant architecture
CREATE TABLE payments (
    id INT(11) NOT NULL AUTO_INCREMENT,
    invoice_name VARCHAR(255) NOT NULL,
    user_id INT(11) NOT NULL,
    company_id INT(11) NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    payment_status ENUM('pending', 'completed', 'failed', 'cancelled', 'processing') NOT NULL DEFAULT 'pending',
    payment_reference VARCHAR(255) DEFAULT NULL,
    payment_gateway VARCHAR(50) NOT NULL DEFAULT 'payfast',
    gateway_transaction_id VARCHAR(255) DEFAULT NULL,
    gateway_response TEXT,
    payment_date TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_company_id (company_id),
    KEY idx_payment_status (payment_status),
    KEY idx_payment_date (payment_date),
    CONSTRAINT fk_payments_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_payments_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sessions table for session management
CREATE TABLE user_sessions (
    id VARCHAR(128) NOT NULL,
    user_id INT(11) NOT NULL,
    company_id INT(11) NOT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_company_id (company_id),
    KEY idx_last_activity (last_activity),
    CONSTRAINT fk_sessions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_sessions_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity logs table for audit trail
CREATE TABLE activity_logs (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) DEFAULT NULL,
    company_id INT(11) DEFAULT NULL,
    action_type ENUM('login', 'logout', 'company_created', 'user_created', 'user_updated', 'user_deleted', 'payment_made', 'payment_failed', 'invoice_downloaded', 'permission_changed', 'system_error') NOT NULL,
    description TEXT,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT,
    metadata JSON DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_company_id (company_id),
    KEY idx_action_type (action_type),
    KEY idx_created_at (created_at),
    CONSTRAINT fk_activity_logs_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_activity_logs_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- INSERT SAMPLE DATA
-- ============================================================================

-- Insert default companies and users
-- Note: These are sample companies for testing the multi-tenant system
INSERT INTO companies (company_name, company_code, address, telephone, email, ftp_folder_path, status) VALUES
('Master System', 'MASTER', 'System Administration', '+27-11-000-0000', '<EMAIL>', '/system/master/', 'active'),
('Demo Company Ltd', 'DEMO001', '123 Demo Street, Demo City, 1234', '+27-11-123-4567', '<EMAIL>', '/invoices/demo001/', 'active'),
('Sample Clearing Co', 'SAMPLE01', '456 Sample Avenue, Sample Town, 5678', '+27-21-987-6543', '<EMAIL>', '/invoices/sample01/', 'active');

-- Insert sample users with hashed passwords
-- Note: All passwords are hashed using PHP's password_hash() function
-- Default passwords: master123 for master admin, admin123 for all others

-- Master Admin user (password: master123)
INSERT INTO users (name, email, password, role, company_id) VALUES
('Master Administrator', '<EMAIL>', '$2y$12$LTVXDglo.oxDbeCgcLoBXOOqvJT3XkvFkFew57YWkRrmH73hYBsZC', 'master_admin', 1);

-- Demo Company Admin (password: admin123)
INSERT INTO users (name, email, password, role, company_id, created_by) VALUES
('Demo Company Admin', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'company_admin', 2, 1);

-- Sample Company Admin (password: admin123)
INSERT INTO users (name, email, password, role, company_id, created_by) VALUES
('Sample Company Admin', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'company_admin', 3, 1);

-- Demo regular users (password: admin123 for all)
INSERT INTO users (name, email, password, role, company_id, created_by) VALUES
('Demo User 1', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'user', 2, 2),
('Demo User 2', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'user', 2, 2),
('Sample User 1', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'user', 3, 3);

-- Module permissions tables
CREATE TABLE IF NOT EXISTS company_modules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    module_name VARCHAR(50) NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by INT,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_company_module (company_id, module_name)
);

CREATE TABLE IF NOT EXISTS user_modules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    module_name VARCHAR(50) NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by INT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_module (user_id, module_name)
);

-- Grant all rights to master admin
INSERT INTO user_rights (user_id, module_name) VALUES
(1, 'invoices'),
(1, 'freight'),
(1, 'tariff'),
(1, 'accounting'),
(1, 'backoffice');

-- Grant all rights to company admins
INSERT INTO user_rights (user_id, module_name) VALUES
(2, 'invoices'),
(2, 'freight'),
(2, 'tariff'),
(2, 'accounting'),
(2, 'backoffice'),
(3, 'invoices'),
(3, 'freight'),
(3, 'tariff'),
(3, 'accounting'),
(3, 'backoffice');

-- Grant limited rights to regular users
INSERT INTO user_rights (user_id, module_name) VALUES
(4, 'invoices'),
(4, 'accounting'),
(5, 'invoices'),
(5, 'freight'),
(6, 'invoices'),
(6, 'tariff');

-- Insert sample activity log entries
INSERT INTO activity_logs (user_id, company_id, action_type, description, ip_address) VALUES
(1, 1, 'company_created', 'Demo Company Ltd created', '127.0.0.1'),
(1, 1, 'company_created', 'Sample Clearing Co created', '127.0.0.1'),
(1, 2, 'user_created', 'Demo Company Admin created', '127.0.0.1'),
(1, 3, 'user_created', 'Sample Company Admin created', '127.0.0.1'),
(2, 2, 'user_created', 'Demo User 1 created', '127.0.0.1'),
(2, 2, 'user_created', 'Demo User 2 created', '127.0.0.1');

-- ============================================================================
-- SCHEMA SETUP COMPLETE
-- ============================================================================
--
-- LOGIN CREDENTIALS FOR TESTING:
--
-- Master Admin:
--   Email: <EMAIL>
--   Password: master123
--   Company Code: MASTER
--
-- Demo Company Admin:
--   Email: <EMAIL>
--   Password: admin123
--   Company Code: DEMO001
--
-- Sample Company Admin:
--   Email: <EMAIL>
--   Password: admin123
--   Company Code: SAMPLE01
--
-- Demo Users:
--   Email: <EMAIL> / <EMAIL>
--   Password: admin123
--   Company Code: DEMO001
--
-- Sample User:
--   Email: <EMAIL>
--   Password: admin123
--   Company Code: SAMPLE01
--
-- ============================================================================
-- DATABASE STRUCTURE OVERVIEW:
-- ============================================================================
--
-- companies: Stores company/tenant information
-- users: Multi-tenant user accounts with role-based access
-- user_rights: Module permissions for users
-- payments: Payment tracking with gateway integration
-- user_sessions: Session management for security
-- activity_logs: Comprehensive audit trail
--
-- FOREIGN KEY RELATIONSHIPS:
-- - users.company_id → companies.id
-- - users.created_by → users.id
-- - user_rights.user_id → users.id
-- - payments.user_id → users.id
-- - payments.company_id → companies.id
-- - user_sessions.user_id → users.id
-- - user_sessions.company_id → companies.id
-- - activity_logs.user_id → users.id
-- - activity_logs.company_id → companies.id
--
-- ============================================================================
