<?php
/**
 * User Management
 * Master Admin interface for managing all users across all companies
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../classes/User.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();
$user = new User($db);

$error_message = '';
$success_message = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        $user_id = $_POST['user_id'] ?? '';
        
        switch ($action) {
            case 'activate':
                $query = "UPDATE users SET status = 'active' WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$user_id])) {
                    $success_message = 'User activated successfully.';
                    logActivity('user_updated', "User ID {$user_id} activated", $_SESSION['user_id']);
                } else {
                    $error_message = 'Failed to activate user.';
                }
                break;
                
            case 'suspend':
                $query = "UPDATE users SET status = 'suspended' WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$user_id])) {
                    $success_message = 'User suspended successfully.';
                    logActivity('user_updated', "User ID {$user_id} suspended", $_SESSION['user_id']);
                } else {
                    $error_message = 'Failed to suspend user.';
                }
                break;
                
            case 'delete':
                $query = "UPDATE users SET status = 'deleted' WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$user_id])) {
                    $success_message = 'User deleted successfully.';
                    logActivity('user_deleted', "User ID {$user_id} deleted", $_SESSION['user_id']);
                } else {
                    $error_message = 'Failed to delete user.';
                }
                break;
        }
    }
}

// Get all users with company information
$search = $_GET['search'] ?? '';
$role_filter = $_GET['role'] ?? '';
$status_filter = $_GET['status'] ?? '';
$company_filter = $_GET['company'] ?? '';

$query = "SELECT u.*, c.company_name, c.company_code 
          FROM users u 
          LEFT JOIN companies c ON u.company_id = c.id 
          WHERE u.status != 'deleted'";

$params = [];

if ($search) {
    $query .= " AND (u.name LIKE ? OR u.email LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($role_filter) {
    $query .= " AND u.role = ?";
    $params[] = $role_filter;
}

if ($status_filter) {
    $query .= " AND u.status = ?";
    $params[] = $status_filter;
}

if ($company_filter) {
    $query .= " AND u.company_id = ?";
    $params[] = $company_filter;
}

$query .= " ORDER BY u.created_at DESC";

$stmt = $db->prepare($query);
$stmt->execute($params);
$users = $stmt->fetchAll();

// Get companies for filter dropdown
$company_query = "SELECT id, company_name FROM companies WHERE status = 'active' ORDER BY company_name";
$company_stmt = $db->prepare($company_query);
$company_stmt->execute();
$companies = $company_stmt->fetchAll();

$csrf_token = generateCsrfToken();
$page_title = "User Management";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .search-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .filter-select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            min-width: 150px;
        }
        
        .users-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .users-table th {
            background: #2B5E5F;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .users-table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .users-table tr:hover {
            background: #f8fafa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-active {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-suspended {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .role-master_admin {
            background: #fef3c7;
            color: #92400e;
        }
        
        .role-company_admin {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .role-user {
            background: #f3f4f6;
            color: #374151;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'users.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">User Management</h1>
            <p class="page-subtitle">Manage all users across all companies</p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="admin-card">
            <!-- Search and Filters -->
            <form method="GET" class="search-filters">
                <input type="text" name="search" class="search-input" 
                       placeholder="Search users by name or email..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                
                <select name="role" class="filter-select">
                    <option value="">All Roles</option>
                    <option value="master_admin" <?php echo $role_filter === 'master_admin' ? 'selected' : ''; ?>>Master Admin</option>
                    <option value="company_admin" <?php echo $role_filter === 'company_admin' ? 'selected' : ''; ?>>Company Admin</option>
                    <option value="user" <?php echo $role_filter === 'user' ? 'selected' : ''; ?>>User</option>
                </select>
                
                <select name="status" class="filter-select">
                    <option value="">All Statuses</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                    <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                </select>
                
                <select name="company" class="filter-select">
                    <option value="">All Companies</option>
                    <?php foreach ($companies as $company): ?>
                        <option value="<?php echo $company['id']; ?>" <?php echo $company_filter == $company['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($company['company_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                
                <button type="submit" class="btn">Search</button>
                <a href="users.php" class="btn btn-secondary">Clear</a>
                <a href="user_create.php" class="btn btn-success">Add User</a>
            </form>
            
            <!-- Users Table -->
            <table class="users-table">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Company</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Last Login</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user_item): ?>
                        <tr>
                            <td>
                                <div style="font-weight: 600;"><?php echo htmlspecialchars($user_item['name']); ?></div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($user_item['email']); ?></div>
                            </td>
                            <td>
                                <?php if ($user_item['company_name']): ?>
                                    <div><?php echo htmlspecialchars($user_item['company_name']); ?></div>
                                    <div style="font-size: 0.85rem; color: #666;">
                                        <?php echo htmlspecialchars($user_item['company_code']); ?>
                                    </div>
                                <?php else: ?>
                                    <span style="color: #999;">No Company</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="role-badge role-<?php echo $user_item['role']; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $user_item['role'])); ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $user_item['status']; ?>">
                                    <?php echo ucfirst($user_item['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($user_item['last_login']): ?>
                                    <?php echo date('M j, Y H:i', strtotime($user_item['last_login'])); ?>
                                <?php else: ?>
                                    <span style="color: #999;">Never</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo date('M j, Y', strtotime($user_item['created_at'])); ?>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="user_edit.php?id=<?php echo $user_item['id']; ?>" class="btn btn-sm">Edit</a>
                                    
                                    <?php if ($user_item['id'] != $_SESSION['user_id']): // Don't allow actions on self ?>
                                        <?php if ($user_item['status'] === 'active'): ?>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to suspend this user?')">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                <input type="hidden" name="action" value="suspend">
                                                <input type="hidden" name="user_id" value="<?php echo $user_item['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-warning">Suspend</button>
                                            </form>
                                        <?php else: ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                <input type="hidden" name="action" value="activate">
                                                <input type="hidden" name="user_id" value="<?php echo $user_item['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-success">Activate</button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="user_id" value="<?php echo $user_item['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if (empty($users)): ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <p>No users found.</p>
                </div>
            <?php endif; ?>
        </div>
    </main>
</body>
</html>
