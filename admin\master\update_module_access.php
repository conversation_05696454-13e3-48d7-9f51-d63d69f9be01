<?php
/**
 * AJAX endpoint for updating user module access
 * Master Admin Portal - Update Module Access
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireMasterAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Only allow AJAX requests
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate CSRF token
    if (!isset($input['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $input['csrf_token'])) {
        throw new Exception('Invalid CSRF token');
    }
    
    // Validate required fields
    if (!isset($input['user_id']) || !isset($input['module']) || !isset($input['has_access'])) {
        throw new Exception('Missing required fields');
    }
    
    $user_id = $input['user_id'];
    $module = $input['module'];
    $has_access = (bool)$input['has_access'];
    
    // Validate user ID format (INT)
    if (!is_numeric($user_id) || $user_id <= 0) {
        throw new Exception('Invalid user ID format');
    }
    
    // Validate module name
    $valid_modules = ['invoices', 'freight', 'tariff', 'accounting', 'backoffice'];
    if (!in_array($module, $valid_modules)) {
        throw new Exception('Invalid module name');
    }
    
    // Verify user exists and get company info
    $user_query = "SELECT u.id, u.company_id, c.status as company_status 
                   FROM users u 
                   JOIN companies c ON u.company_id = c.id 
                   WHERE u.id = ? AND u.status != 'deleted'";
    $user_stmt = $db->prepare($user_query);
    $user_stmt->execute([$user_id]);
    $user = $user_stmt->fetch();
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    if ($user['company_status'] !== 'active') {
        throw new Exception('Cannot modify access for users in inactive companies');
    }
    
    // Begin transaction
    $db->beginTransaction();
    
    try {
        if ($has_access) {
            // Add module access
            $insert_query = "INSERT INTO user_rights (user_id, module_name) VALUES (?, ?) 
                           ON CONFLICT (user_id, module_name) DO NOTHING";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->execute([$user_id, $module]);
        } else {
            // Remove module access
            $delete_query = "DELETE FROM user_rights WHERE user_id = ? AND module_name = ?";
            $delete_stmt = $db->prepare($delete_query);
            $delete_stmt->execute([$user_id, $module]);
        }
        
        // Log the activity
        $action = $has_access ? 'granted' : 'revoked';
        $activity_query = "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) 
                          VALUES (?, ?, ?, ?, ?, NOW())";
        $activity_stmt = $db->prepare($activity_query);
        $activity_stmt->execute([
            $_SESSION['user_id'],
            'module_access_updated',
            "Module access {$action} for user {$user_id}: {$module}",
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        // Commit transaction
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Module access updated successfully'
        ]);
        
    } catch (Exception $e) {
        $db->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Module access update error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
