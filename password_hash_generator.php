<?php
/**
 * Password Hash Generator
 * Generate correct password hashes for the portal users
 */

echo "<h2>Password Hash Generator</h2>";
echo "<p>This script generates the correct password hashes for all portal users.</p>";

// Define all users and their passwords
$users = [
    'Master Admin' => [
        'email' => '<EMAIL>',
        'password' => 'master123'
    ],
    'Demo Company Admin' => [
        'email' => '<EMAIL>',
        'password' => 'admin123'
    ],
    'Sample Company Admin' => [
        'email' => '<EMAIL>',
        'password' => 'admin123'
    ],
    'Demo User 1' => [
        'email' => '<EMAIL>',
        'password' => 'admin123'
    ],
    'Demo User 2' => [
        'email' => '<EMAIL>',
        'password' => 'admin123'
    ],
    'Sample User 1' => [
        'email' => '<EMAIL>',
        'password' => 'admin123'
    ]
];

echo "<h3>Generated Password Hashes:</h3>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f0f0f0;'>";
echo "<th style='padding: 10px; text-align: left;'>User</th>";
echo "<th style='padding: 10px; text-align: left;'>Email</th>";
echo "<th style='padding: 10px; text-align: left;'>Password</th>";
echo "<th style='padding: 10px; text-align: left;'>Generated Hash</th>";
echo "</tr>";

$sql_updates = [];

foreach ($users as $name => $data) {
    $hash = password_hash($data['password'], PASSWORD_DEFAULT);
    
    echo "<tr>";
    echo "<td style='padding: 10px;'>" . htmlspecialchars($name) . "</td>";
    echo "<td style='padding: 10px;'>" . htmlspecialchars($data['email']) . "</td>";
    echo "<td style='padding: 10px;'>" . htmlspecialchars($data['password']) . "</td>";
    echo "<td style='padding: 10px; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($hash) . "</td>";
    echo "</tr>";
    
    // Generate SQL update statement
    $sql_updates[] = "UPDATE users SET password = '" . $hash . "' WHERE email = '" . $data['email'] . "';";
}

echo "</table>";

echo "<h3>SQL Update Statements:</h3>";
echo "<p>Copy and paste these SQL statements into phpMyAdmin to update all user passwords:</p>";

echo "<textarea style='width: 100%; height: 200px; font-family: monospace;'>";
echo "-- Update all user passwords with correct hashes\n";
echo "-- Generated on " . date('Y-m-d H:i:s') . "\n\n";

foreach ($sql_updates as $sql) {
    echo $sql . "\n";
}

echo "\n-- Verify the updates\n";
echo "SELECT u.name, u.email, u.role, c.company_code, u.status\n";
echo "FROM users u \n";
echo "LEFT JOIN companies c ON u.company_id = c.id \n";
echo "ORDER BY u.id;";
echo "</textarea>";

echo "<h3>Test Password Verification:</h3>";
echo "<p>Testing if the generated hashes work correctly:</p>";

foreach ($users as $name => $data) {
    $hash = password_hash($data['password'], PASSWORD_DEFAULT);
    $verification = password_verify($data['password'], $hash);
    
    $status = $verification ? "✓ PASS" : "✗ FAIL";
    $color = $verification ? "green" : "red";
    
    echo "<p style='color: $color;'>$status - {$data['email']} with password '{$data['password']}'</p>";
}

echo "<h3>Instructions:</h3>";
echo "<ol>";
echo "<li><strong>Copy the SQL statements</strong> from the textarea above</li>";
echo "<li><strong>Open phpMyAdmin</strong> and select your 'portal_app' database</li>";
echo "<li><strong>Go to the SQL tab</strong></li>";
echo "<li><strong>Paste the SQL statements</strong> and click 'Go'</li>";
echo "<li><strong>Test login</strong> with any of the credentials shown in the table above</li>";
echo "</ol>";

echo "<h3>Login Credentials After Update:</h3>";
echo "<div style='background: #e6f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Master Admin:</h4>";
echo "<ul>";
echo "<li>Email: <EMAIL></li>";
echo "<li>Password: master123</li>";
echo "<li>Company Code: MASTER</li>";
echo "</ul>";

echo "<h4>Demo Company Admin:</h4>";
echo "<ul>";
echo "<li>Email: <EMAIL></li>";
echo "<li>Password: admin123</li>";
echo "<li>Company Code: DEMO001</li>";
echo "</ul>";

echo "<h4>Sample Company Admin:</h4>";
echo "<ul>";
echo "<li>Email: <EMAIL></li>";
echo "<li>Password: admin123</li>";
echo "<li>Company Code: SAMPLE01</li>";
echo "</ul>";

echo "<h4>Demo Users:</h4>";
echo "<ul>";
echo "<li>Email: user1@democompany.<NAME_EMAIL></li>";
echo "<li>Password: admin123</li>";
echo "<li>Company Code: DEMO001</li>";
echo "</ul>";

echo "<h4>Sample User:</h4>";
echo "<ul>";
echo "<li>Email: <EMAIL></li>";
echo "<li>Password: admin123</li>";
echo "<li>Company Code: SAMPLE01</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><a href='login.php'>Test Login Page</a></p>";
?>
