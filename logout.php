<?php
/**
 * Logout Page
 * Secure logout with session cleanup
 */

require_once 'config/config.php';

// Log the logout action
if (isLoggedIn()) {
    logSecurityEvent('USER_LOGOUT', 'User: ' . $_SESSION['user_email']);
}

// Clear session and redirect
session_unset();
session_destroy();

// Clear session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

header('Location: login.php?message=logged_out');
exit();
?>
