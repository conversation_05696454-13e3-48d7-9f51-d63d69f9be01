<?php
/**
 * Main Entry Point
 * Redirects to appropriate page based on demo/installation status
 */

require_once 'config/config.php';

// Check if we're in demo mode
if (defined('FTP_DEMO_MODE') && FTP_DEMO_MODE) {
    // Demo mode - show demo status or redirect based on login
    if (isLoggedIn()) {
        header('Location: dashboard.php');
        exit();
    } else {
        header('Location: demo_status.php');
        exit();
    }
}

// Check if application is installed (for production mode)
if (!file_exists('config/installed.lock')) {
    // Not installed, redirect to installation
    header('Location: install.php');
    exit();
}

// Check if user is already logged in
if (isLoggedIn()) {
    // User is logged in, redirect to dashboard
    header('Location: dashboard.php');
    exit();
} else {
    // User not logged in, redirect to login page
    header('Location: login.php');
    exit();
}
?>
