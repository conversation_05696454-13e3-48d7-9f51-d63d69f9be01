<?php
/**
 * Company Management
 * Master Admin interface for managing all companies
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        $company_id = $_POST['company_id'] ?? '';
        
        switch ($action) {
            case 'activate':
                $query = "UPDATE companies SET status = 'active' WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$company_id])) {
                    $success_message = 'Company activated successfully.';
                    logActivity('company_updated', "Company ID {$company_id} activated", $_SESSION['user_id']);
                } else {
                    $error_message = 'Failed to activate company.';
                }
                break;
                
            case 'suspend':
                $query = "UPDATE companies SET status = 'suspended' WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$company_id])) {
                    $success_message = 'Company suspended successfully.';
                    logActivity('company_updated', "Company ID {$company_id} suspended", $_SESSION['user_id']);
                } else {
                    $error_message = 'Failed to suspend company.';
                }
                break;
        }
    }
}

// Get all companies with user counts
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';

$query = "SELECT c.*, 
                 COUNT(u.id) as user_count,
                 SUM(CASE WHEN u.status = 'active' THEN 1 ELSE 0 END) as active_users
          FROM companies c 
          LEFT JOIN users u ON c.id = u.company_id 
          WHERE 1=1";

$params = [];

if ($search) {
    $query .= " AND (c.company_name LIKE ? OR c.company_code LIKE ? OR c.email LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($status_filter) {
    $query .= " AND c.status = ?";
    $params[] = $status_filter;
}

$query .= " GROUP BY c.id ORDER BY c.created_at DESC";

$stmt = $db->prepare($query);
$stmt->execute($params);
$companies = $stmt->fetchAll();

$csrf_token = generateCsrfToken();
$page_title = "Company Management";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .search-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .filter-select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }
        
        .companies-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .companies-table th {
            background: #2B5E5F;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .companies-table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .companies-table tr:hover {
            background: #f8fafa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-active {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-suspended {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'companies.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Company Management</h1>
            <p class="page-subtitle">Manage all companies in the system</p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="admin-card">
            <!-- Search and Filters -->
            <form method="GET" class="search-filters">
                <input type="text" name="search" class="search-input" 
                       placeholder="Search companies by name, code, or email..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                
                <select name="status" class="filter-select">
                    <option value="">All Statuses</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                    <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                </select>
                
                <button type="submit" class="btn">Search</button>
                <a href="companies.php" class="btn btn-secondary">Clear</a>
                <a href="company_registration.php" class="btn btn-success">Add Company</a>
            </form>
            
            <!-- Companies Table -->
            <table class="companies-table">
                <thead>
                    <tr>
                        <th>Company</th>
                        <th>Code</th>
                        <th>Contact</th>
                        <th>Users</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($companies as $company): ?>
                        <tr>
                            <td>
                                <div style="font-weight: 600;"><?php echo htmlspecialchars($company['company_name']); ?></div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($company['email']); ?></div>
                            </td>
                            <td>
                                <code style="background: #f0f0f0; padding: 0.25rem 0.5rem; border-radius: 4px;">
                                    <?php echo htmlspecialchars($company['company_code']); ?>
                                </code>
                            </td>
                            <td>
                                <?php if ($company['telephone']): ?>
                                    <div><?php echo htmlspecialchars($company['telephone']); ?></div>
                                <?php endif; ?>
                                <?php if ($company['website']): ?>
                                    <div style="font-size: 0.85rem; color: #666;">
                                        <a href="<?php echo htmlspecialchars($company['website']); ?>" target="_blank">
                                            <?php echo htmlspecialchars($company['website']); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div><?php echo $company['active_users']; ?> active</div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo $company['user_count']; ?> total</div>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $company['status']; ?>">
                                    <?php echo ucfirst($company['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo date('M j, Y', strtotime($company['created_at'])); ?>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="company_edit.php?id=<?php echo $company['id']; ?>" class="btn btn-sm">Edit</a>
                                    
                                    <?php if ($company['status'] === 'active'): ?>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to suspend this company?')">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                            <input type="hidden" name="action" value="suspend">
                                            <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-warning">Suspend</button>
                                        </form>
                                    <?php else: ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                            <input type="hidden" name="action" value="activate">
                                            <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-success">Activate</button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if (empty($companies)): ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <p>No companies found.</p>
                    <a href="company_registration.php" class="btn" style="margin-top: 1rem;">Register First Company</a>
                </div>
            <?php endif; ?>
        </div>
    </main>
</body>
</html>
