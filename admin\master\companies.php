<?php
/**
 * Company Management
 * Master Admin interface for managing all companies
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        $company_id = $_POST['company_id'] ?? '';
        
        switch ($action) {
            case 'activate':
                $query = "UPDATE companies SET status = 'active' WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$company_id])) {
                    $success_message = 'Company activated successfully.';
                    logActivity('company_updated', "Company ID {$company_id} activated", $_SESSION['user_id']);
                } else {
                    $error_message = 'Failed to activate company.';
                }
                break;
                
            case 'suspend':
                $query = "UPDATE companies SET status = 'suspended' WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$company_id])) {
                    $success_message = 'Company suspended successfully.';
                    logActivity('company_updated', "Company ID {$company_id} suspended", $_SESSION['user_id']);
                } else {
                    $error_message = 'Failed to suspend company.';
                }
                break;
        }
    }
}

// Get all companies with user counts
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';

$query = "SELECT c.*, 
                 COUNT(u.id) as user_count,
                 SUM(CASE WHEN u.status = 'active' THEN 1 ELSE 0 END) as active_users
          FROM companies c 
          LEFT JOIN users u ON c.id = u.company_id 
          WHERE 1=1";

$params = [];

if ($search) {
    $query .= " AND (c.company_name LIKE ? OR c.company_code LIKE ? OR c.email LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($status_filter) {
    $query .= " AND c.status = ?";
    $params[] = $status_filter;
}

$query .= " GROUP BY c.id ORDER BY c.created_at DESC";

$stmt = $db->prepare($query);
$stmt->execute($params);
$companies = $stmt->fetchAll();

// Get users for each company
$company_users = [];
if (!empty($companies)) {
    $company_ids = array_column($companies, 'id');
    $placeholders = str_repeat('?,', count($company_ids) - 1) . '?';

    $users_query = "SELECT u.id, u.name, u.email, u.role, u.status, u.company_id, u.last_login, u.created_at
                    FROM users u
                    WHERE u.company_id IN ($placeholders) AND u.status != 'deleted'
                    ORDER BY u.company_id, u.name";

    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute($company_ids);
    $all_users = $users_stmt->fetchAll();

    // Group users by company
    foreach ($all_users as $user) {
        $company_users[$user['company_id']][] = $user;
    }
}

$csrf_token = generateCsrfToken();
$page_title = "Company Management";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .search-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .filter-select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }
        
        .companies-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .companies-table th {
            background: #2B5E5F;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .companies-table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .companies-table tr:hover {
            background: #f8fafa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-active {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-suspended {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'companies.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Company Management</h1>
            <p class="page-subtitle">Manage all companies in the system</p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="admin-card">
            <!-- Search and Filters -->
            <form method="GET" class="search-filters">
                <input type="text" name="search" class="search-input" 
                       placeholder="Search companies by name, code, or email..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                
                <select name="status" class="filter-select">
                    <option value="">All Statuses</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                    <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                </select>
                
                <button type="submit" class="btn">Search</button>
                <a href="companies.php" class="btn btn-secondary">Clear</a>
                <a href="company_registration.php" class="btn btn-success">Add Company</a>
            </form>
            
            <!-- Companies Table -->
            <table class="companies-table">
                <thead>
                    <tr>
                        <th>Company</th>
                        <th>Code</th>
                        <th>Contact</th>
                        <th>Users</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($companies as $company): ?>
                        <tr>
                            <td>
                                <div style="font-weight: 600;"><?php echo htmlspecialchars($company['company_name']); ?></div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($company['email']); ?></div>
                            </td>
                            <td>
                                <code style="background: #f0f0f0; padding: 0.25rem 0.5rem; border-radius: 4px;">
                                    <?php echo htmlspecialchars($company['company_code']); ?>
                                </code>
                            </td>
                            <td>
                                <?php if ($company['telephone']): ?>
                                    <div><?php echo htmlspecialchars($company['telephone']); ?></div>
                                <?php endif; ?>
                                <?php if ($company['website']): ?>
                                    <div style="font-size: 0.85rem; color: #666;">
                                        <a href="<?php echo htmlspecialchars($company['website']); ?>" target="_blank">
                                            <?php echo htmlspecialchars($company['website']); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div><?php echo $company['active_users']; ?> active</div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo $company['user_count']; ?> total</div>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $company['status']; ?>">
                                    <?php echo ucfirst($company['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo date('M j, Y', strtotime($company['created_at'])); ?>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="company_edit.php?id=<?php echo $company['id']; ?>" class="btn btn-sm">Edit</a>

                                    <div class="users-dropdown-container">
                                        <button class="btn btn-sm users-dropdown-trigger" onclick="toggleUsersDropdown(<?php echo $company['id']; ?>)">
                                            Users
                                            <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                                                <path d="M6 8L2 4h8l-4 4z"/>
                                            </svg>
                                        </button>

                                        <div class="users-dropdown" id="usersDropdown<?php echo $company['id']; ?>">
                                            <div class="users-dropdown-header">
                                                <h4><?php echo htmlspecialchars($company['company_name']); ?> Users</h4>
                                            </div>
                                            <div class="users-dropdown-content">
                                                <?php if (isset($company_users[$company['id']]) && !empty($company_users[$company['id']])): ?>
                                                    <?php foreach ($company_users[$company['id']] as $user): ?>
                                                        <div class="user-item">
                                                            <div class="user-info">
                                                                <div class="user-name"><?php echo htmlspecialchars($user['name']); ?></div>
                                                                <div class="user-details">
                                                                    <span class="user-email"><?php echo htmlspecialchars($user['email']); ?></span>
                                                                    <span class="user-role role-<?php echo $user['role']; ?>"><?php echo ucfirst(str_replace('_', ' ', $user['role'])); ?></span>
                                                                    <span class="user-status status-<?php echo $user['status']; ?>"><?php echo ucfirst($user['status']); ?></span>
                                                                </div>
                                                            </div>
                                                            <div class="user-actions">
                                                                <a href="user_edit.php?id=<?php echo $user['id']; ?>" class="btn btn-xs">Edit</a>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <div class="no-users">
                                                        <p>No users found for this company</p>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if ($company['status'] === 'active'): ?>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to suspend this company?')">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                            <input type="hidden" name="action" value="suspend">
                                            <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-warning">Suspend</button>
                                        </form>
                                    <?php else: ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                            <input type="hidden" name="action" value="activate">
                                            <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-success">Activate</button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if (empty($companies)): ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <p>No companies found.</p>
                    <a href="company_registration.php" class="btn" style="margin-top: 1rem;">Register First Company</a>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <style>
        /* Improved Action Buttons Layout */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .action-buttons .btn {
            white-space: nowrap;
        }

        /* Users Dropdown Container */
        .users-dropdown-container {
            position: relative;
            display: inline-block;
        }

        .users-dropdown-trigger {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            color: #374151;
            transition: all 0.2s ease;
        }

        .users-dropdown-trigger:hover {
            background: #f1f5f9;
            border-color: #2B5E5F;
            color: #2B5E5F;
        }

        .dropdown-arrow {
            transition: transform 0.2s ease;
            color: currentColor;
        }

        .users-dropdown-trigger.active {
            background: #2B5E5F;
            border-color: #2B5E5F;
            color: white;
        }

        .users-dropdown-trigger.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        .users-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            margin-top: 0.5rem;
            min-width: 400px;
            max-width: 500px;
        }

        .users-dropdown.show {
            display: block;
        }

        .users-dropdown-header {
            padding: 1rem 1.25rem 0.5rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .users-dropdown-header h4 {
            margin: 0;
            font-size: 0.9rem;
            font-weight: 600;
            color: #374151;
        }

        .users-dropdown-content {
            max-height: 300px;
            overflow-y: auto;
        }

        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1.25rem;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.2s ease;
        }

        .user-item:hover {
            background: #f8fafc;
        }

        .user-item:last-child {
            border-bottom: none;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .user-details {
            display: flex;
            gap: 0.75rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .user-email {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .user-role, .user-status {
            font-size: 0.75rem;
            padding: 0.125rem 0.5rem;
            border-radius: 12px;
            font-weight: 500;
        }

        .role-master_admin {
            background: #fef3c7;
            color: #92400e;
        }

        .role-company_admin {
            background: #dbeafe;
            color: #1e40af;
        }

        .role-user {
            background: #f3f4f6;
            color: #374151;
        }

        .status-active {
            background: #d1fae5;
            color: #065f46;
        }

        .status-inactive {
            background: #f3f4f6;
            color: #6b7280;
        }

        .status-suspended {
            background: #fee2e2;
            color: #991b1b;
        }

        .user-actions {
            margin-left: 1rem;
        }

        .btn-xs {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
            border-radius: 4px;
        }

        .no-users {
            padding: 1.5rem;
            text-align: center;
            color: #6b7280;
        }

        .no-users p {
            margin: 0;
            font-size: 0.9rem;
        }
    </style>

    <script>
        function toggleUsersDropdown(companyId) {
            // Close all other dropdowns
            const allDropdowns = document.querySelectorAll('.users-dropdown');
            const allTriggers = document.querySelectorAll('.users-dropdown-trigger');

            allDropdowns.forEach(dropdown => {
                if (dropdown.id !== `usersDropdown${companyId}`) {
                    dropdown.classList.remove('show');
                }
            });

            allTriggers.forEach(trigger => {
                if (trigger !== event.target.closest('.users-dropdown-trigger')) {
                    trigger.classList.remove('active');
                }
            });

            // Toggle current dropdown
            const dropdown = document.getElementById(`usersDropdown${companyId}`);
            const trigger = event.target.closest('.users-dropdown-trigger');

            if (dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
                trigger.classList.remove('active');
            } else {
                dropdown.classList.add('show');
                trigger.classList.add('active');
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.users-dropdown-container')) {
                const allDropdowns = document.querySelectorAll('.users-dropdown');
                const allTriggers = document.querySelectorAll('.users-dropdown-trigger');

                allDropdowns.forEach(dropdown => dropdown.classList.remove('show'));
                allTriggers.forEach(trigger => trigger.classList.remove('active'));
            }
        });
    </script>
</body>
</html>
