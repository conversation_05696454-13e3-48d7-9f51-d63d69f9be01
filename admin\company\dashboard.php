<?php
/**
 * Company Admin Dashboard
 * Interface for company administrators to manage their company's users and operations
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../classes/User.php';
require_once '../shared/admin_config.php';

// Require login and company admin privileges
requireLogin();
requireAdminAccess('company');

$database = new Database();
$db = $database->getConnection();
$user = new User($db);

// Get company-specific statistics
$stats = [];
$company_id = $_SESSION['company_id'];

// Total users in company
$query = "SELECT COUNT(*) as total FROM users WHERE company_id = ? AND status = 'active'";
$stmt = $db->prepare($query);
$stmt->execute([$company_id]);
$stats['company_users'] = $stmt->fetch()['total'];

// Total payments for company this month
$query = "SELECT COUNT(*) as total, SUM(amount) as amount FROM payments 
          WHERE company_id = ? AND payment_status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE())";
$stmt = $db->prepare($query);
$stmt->execute([$company_id]);
$payment_stats = $stmt->fetch();
$stats['monthly_payments'] = $payment_stats['total'] ?? 0;
$stats['monthly_amount'] = $payment_stats['amount'] ?? 0;

// Outstanding payments
$query = "SELECT COUNT(*) as total, SUM(amount) as amount FROM payments 
          WHERE company_id = ? AND payment_status = 'pending'";
$stmt = $db->prepare($query);
$stmt->execute([$company_id]);
$outstanding_stats = $stmt->fetch();
$stats['outstanding_payments'] = $outstanding_stats['total'] ?? 0;
$stats['outstanding_amount'] = $outstanding_stats['amount'] ?? 0;

// Recent company users
$query = "SELECT * FROM users WHERE company_id = ? ORDER BY created_at DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute([$company_id]);
$recent_users = $stmt->fetchAll();

// Recent company activity
$query = "SELECT al.*, u.name as user_name 
          FROM activity_logs al 
          LEFT JOIN users u ON al.user_id = u.id 
          WHERE al.company_id = ? 
          ORDER BY al.created_at DESC LIMIT 10";
$stmt = $db->prepare($query);
$stmt->execute([$company_id]);
$recent_activities = $stmt->fetchAll();

// Recent payments
$query = "SELECT * FROM payments WHERE company_id = ? ORDER BY created_at DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute([$company_id]);
$recent_payments = $stmt->fetchAll();

$page_title = "Company Admin Dashboard";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('company'); ?>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'company', 'dashboard.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Company Admin Dashboard</h1>
            <p class="page-subtitle">Manage your company's users and operations - <?php echo htmlspecialchars($_SESSION['company_name']); ?></p>
        </div>
        
        <!-- Company Statistics -->
        <div class="admin-stats">
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['company_users']); ?></div>
                <div class="stat-label">Company Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['monthly_payments']); ?></div>
                <div class="stat-label">Monthly Payments</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">R<?php echo number_format($stats['monthly_amount'], 2); ?></div>
                <div class="stat-label">Monthly Amount</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['outstanding_payments']); ?></div>
                <div class="stat-label">Outstanding Payments</div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="admin-actions">
            <a href="users.php?action=create" class="admin-action-btn">
                👤 Add New User
            </a>
            <a href="users.php" class="admin-action-btn">
                👥 Manage Users
            </a>
            <a href="payments.php" class="admin-action-btn">
                💳 View Payments
            </a>
            <a href="reports.php" class="admin-action-btn">
                📊 Generate Reports
            </a>
            <a href="settings.php" class="admin-action-btn">
                ⚙️ Company Settings
            </a>
            <a href="../../dashboard.php" class="admin-action-btn">
                🏠 Main System
            </a>
        </div>

        <!-- Recent Activity and Management Sections -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin: 2rem 0;">
            <!-- Recent Users -->
            <div class="admin-card">
                <h3 style="margin-bottom: 1.5rem; color: #2B5E5F;">Recent Users</h3>
                <div class="user-list">
                    <?php foreach ($recent_users as $user_item): ?>
                        <div style="padding: 1rem; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600;"><?php echo htmlspecialchars($user_item['name']); ?></div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($user_item['email']); ?></div>
                                <div style="font-size: 0.8rem; color: #999;">
                                    <?php echo ucfirst($user_item['role']); ?> • 
                                    <?php echo date('M j, Y', strtotime($user_item['created_at'])); ?>
                                </div>
                            </div>
                            <div>
                                <a href="users.php?id=<?php echo $user_item['id']; ?>" class="btn btn-sm">View</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div style="margin-top: 1rem; text-align: center;">
                    <a href="users.php" class="btn">View All Users</a>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="admin-card">
                <h3 style="margin-bottom: 1.5rem; color: #2B5E5F;">Recent Payments</h3>
                <div class="payment-list">
                    <?php foreach ($recent_payments as $payment): ?>
                        <div style="padding: 1rem; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600;">R<?php echo number_format($payment['amount'], 2); ?></div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($payment['description'] ?? 'Payment'); ?></div>
                                <div style="font-size: 0.8rem; color: #999;">
                                    <?php echo date('M j, Y', strtotime($payment['created_at'])); ?>
                                </div>
                            </div>
                            <div>
                                <span class="status-badge status-<?php echo $payment['payment_status']; ?>">
                                    <?php echo ucfirst($payment['payment_status']); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div style="margin-top: 1rem; text-align: center;">
                    <a href="payments.php" class="btn">View All Payments</a>
                </div>
            </div>
        </div>

        <!-- Recent Activity Log -->
        <div class="admin-card">
            <h3 style="margin-bottom: 1.5rem; color: #2B5E5F;">Recent Company Activity</h3>
            <div class="activity-log">
                <?php foreach ($recent_activities as $activity): ?>
                    <div style="padding: 1rem; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                        <div style="flex: 1;">
                            <div style="font-weight: 600; margin-bottom: 0.25rem;">
                                <?php
                                $action_icons = [
                                    'login' => '🔐',
                                    'logout' => '🚪',
                                    'user_created' => '👤',
                                    'user_updated' => '✏️',
                                    'payment_made' => '💳',
                                    'invoice_downloaded' => '📥'
                                ];
                                echo $action_icons[$activity['action_type']] ?? '📝';
                                ?>
                                <?php echo htmlspecialchars($activity['description']); ?>
                            </div>
                            <div style="font-size: 0.8rem; color: #666;">
                                <?php if ($activity['user_name']): ?>
                                    by <?php echo htmlspecialchars($activity['user_name']); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div style="font-size: 0.8rem; color: #999; text-align: right;">
                            <?php echo date('M j, Y H:i', strtotime($activity['created_at'])); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div style="margin-top: 1rem; text-align: center;">
                <a href="activity.php" class="btn">View Full Activity Log</a>
            </div>
        </div>
    </main>
    
    <style>
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }
    </style>
</body>
</html>
