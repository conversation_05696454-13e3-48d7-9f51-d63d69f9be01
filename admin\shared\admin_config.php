<?php
/**
 * Shared Admin Configuration
 * Common functions and styles for all admin interfaces
 */

// Prevent direct access
if (!defined('ADMIN_ACCESS')) {
    die('Direct access not permitted');
}

// Admin color scheme based on login page
define('ADMIN_PRIMARY_COLOR', '#2B5E5F');
define('ADMIN_SECONDARY_COLOR', '#1a4344');
define('ADMIN_ACCENT_COLOR', '#3a7273');
define('ADMIN_LIGHT_COLOR', '#f8fafa');
define('ADMIN_SUCCESS_COLOR', '#10b981');
define('ADMIN_WARNING_COLOR', '#f59e0b');
define('ADMIN_ERROR_COLOR', '#ef4444');

/**
 * Generate admin header with navigation
 */
function generateAdminHeader($page_title, $admin_type = 'master', $active_nav = '') {
    $logo_path = ($admin_type === 'master') ? '../../assets/img/logo.png' : '../../assets/img/logo.png';
    $dashboard_link = ($admin_type === 'master') ? 'dashboard.php' : 'dashboard.php';
    
    $nav_items = [];
    if ($admin_type === 'master') {
        $nav_items = [
            'dashboard.php' => 'Dashboard',
            'companies.php' => 'Companies',
            'users.php' => 'Users',
            'modules.php' => 'Module Access',
            'payments.php' => 'Payments',
            'activity.php' => 'Activity Logs',
            'settings.php' => 'Settings'
        ];
    } else {
        $nav_items = [
            'dashboard.php' => 'Dashboard',
            'users.php' => 'Manage Users',
            'payments.php' => 'Payments',
            'reports.php' => 'Reports',
            'settings.php' => 'Settings',
            '../../dashboard.php' => 'Main System'
        ];
    }
    
    ob_start();
    ?>
    <header class="admin-header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-logo">
                        <img src="<?php echo $logo_path; ?>" alt="Portal Logo" class="logo-image">
                    </div>
                    <div class="brand-text">
                        <span class="brand-subtitle"><?php echo $admin_type === 'master' ? 'Master Admin Portal' : 'Company Admin Portal'; ?></span>
                    </div>
                </div>
                <nav class="main-navigation">
                    <?php foreach ($nav_items as $url => $label): ?>
                        <a href="<?php echo $url; ?>" class="nav-item <?php echo ($active_nav === $url) ? 'active' : ''; ?>">
                            <?php echo $label; ?>
                        </a>
                    <?php endforeach; ?>
                </nav>
            </div>
            
            <div class="header-right">
                <div class="professional-actions">
                    <div class="user-menu-container">
                        <button class="professional-btn user-menu-trigger" onclick="toggleUserMenu()">
                            <span class="user-name"><?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M8 11L3 6h10l-5 5z"/>
                            </svg>
                        </button>
                        <div class="user-menu" id="userMenu">
                            <div class="user-menu-header">
                                <div class="user-menu-name"><?php echo htmlspecialchars($_SESSION['user_name']); ?></div>
                                <div class="user-menu-role"><?php echo $admin_type === 'master' ? 'Master Administrator' : 'Company Administrator'; ?></div>
                                <div class="user-menu-company"><?php echo htmlspecialchars($_SESSION['company_name'] ?? ''); ?></div>
                            </div>
                            <div class="user-menu-items">
                                <a href="../../profile.php" class="user-menu-item">Profile Settings</a>
                                <a href="../../account.php" class="user-menu-item">Account Settings</a>
                                <a href="../../help.php" class="user-menu-item">Help & Support</a>
                                <div class="user-menu-divider"></div>
                                <a href="../../logout.php" class="user-menu-item logout">Logout</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <?php
    return ob_get_clean();
}

/**
 * Generate admin CSS styles
 */
function generateAdminCSS($admin_type = 'master') {
    ob_start();
    ?>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, <?php echo ADMIN_LIGHT_COLOR; ?> 0%, #e8f4f4 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .admin-header {
            background: <?php echo ADMIN_PRIMARY_COLOR; ?>;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(43, 94, 95, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            height: 70px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 3rem;
        }
        
        .brand-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logo-image {
            height: 40px;
            width: auto;
        }
        
        .brand-subtitle {
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
        }
        
        .main-navigation {
            display: flex;
            gap: 0.5rem;
        }
        
        .nav-item {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            padding: 0.75rem 1.25rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
            font-size: 0.95rem;
        }
        
        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .nav-item.active {
            background: <?php echo ADMIN_SECONDARY_COLOR; ?>;
            color: white;
        }
        
        .professional-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.75rem 1.25rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.2s ease;
            font-size: 0.95rem;
        }
        
        .professional-btn:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }
        
        .user-menu-container {
            position: relative;
        }
        
        .user-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            min-width: 280px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 0.5rem;
        }
        
        .user-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-menu-header {
            padding: 1.5rem;
            border-bottom: 1px solid #f0f0f0;
            background: <?php echo ADMIN_LIGHT_COLOR; ?>;
            border-radius: 12px 12px 0 0;
        }
        
        .user-menu-name {
            font-weight: 600;
            color: <?php echo ADMIN_PRIMARY_COLOR; ?>;
            font-size: 1.1rem;
        }
        
        .user-menu-role {
            color: <?php echo ADMIN_ACCENT_COLOR; ?>;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
        
        .user-menu-company {
            color: #666;
            font-size: 0.85rem;
            margin-top: 0.25rem;
        }
        
        .user-menu-items {
            padding: 0.75rem 0;
        }
        
        .user-menu-item {
            display: block;
            padding: 0.75rem 1.5rem;
            color: #333;
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 0.95rem;
        }
        
        .user-menu-item:hover {
            background: <?php echo ADMIN_LIGHT_COLOR; ?>;
            color: <?php echo ADMIN_PRIMARY_COLOR; ?>;
        }
        
        .user-menu-item.logout {
            color: <?php echo ADMIN_ERROR_COLOR; ?>;
            border-top: 1px solid #f0f0f0;
            margin-top: 0.5rem;
        }
        
        .user-menu-divider {
            height: 1px;
            background: #f0f0f0;
            margin: 0.5rem 0;
        }
        
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .admin-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
            border-left: 4px solid <?php echo ADMIN_PRIMARY_COLOR; ?>;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: <?php echo ADMIN_PRIMARY_COLOR; ?>;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
            font-size: 0.95rem;
        }
        
        .btn {
            background: <?php echo ADMIN_PRIMARY_COLOR; ?>;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
            font-size: 0.95rem;
        }
        
        .btn:hover {
            background: <?php echo ADMIN_SECONDARY_COLOR; ?>;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: <?php echo ADMIN_ACCENT_COLOR; ?>;
        }
        
        .btn-success {
            background: <?php echo ADMIN_SUCCESS_COLOR; ?>;
        }
        
        .btn-warning {
            background: <?php echo ADMIN_WARNING_COLOR; ?>;
        }
        
        .btn-danger {
            background: <?php echo ADMIN_ERROR_COLOR; ?>;
        }
        
        .admin-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .admin-action-btn {
            background: white;
            border: 2px solid <?php echo ADMIN_PRIMARY_COLOR; ?>;
            color: <?php echo ADMIN_PRIMARY_COLOR; ?>;
            padding: 1.25rem;
            border-radius: 12px;
            text-decoration: none;
            text-align: center;
            font-weight: 600;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }
        
        .admin-action-btn:hover {
            background: <?php echo ADMIN_PRIMARY_COLOR; ?>;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(43, 94, 95, 0.2);
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: <?php echo ADMIN_PRIMARY_COLOR; ?>;
            margin-bottom: 1rem;
        }
        
        .page-subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }
        
        @media (max-width: 768px) {
            .header-content {
                padding: 0 1rem;
                flex-direction: column;
                height: auto;
                padding-top: 1rem;
                padding-bottom: 1rem;
            }
            
            .header-left {
                flex-direction: column;
                gap: 1rem;
                width: 100%;
            }
            
            .main-navigation {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .admin-stats {
                grid-template-columns: 1fr;
            }
            
            .admin-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    <script>
        function toggleUserMenu() {
            const menu = document.getElementById('userMenu');
            menu.classList.toggle('show');
        }
        
        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const container = document.querySelector('.user-menu-container');
            const menu = document.getElementById('userMenu');
            
            if (!container.contains(event.target)) {
                menu.classList.remove('show');
            }
        });
    </script>
    <?php
    return ob_get_clean();
}

/**
 * Require admin access
 */
function requireAdminAccess($admin_type = 'master') {
    if (!isset($_SESSION['user_id'])) {
        header('Location: ../../login.php');
        exit();
    }
    
    if ($admin_type === 'master' && !isMasterAdmin()) {
        header('Location: ../../dashboard.php');
        exit();
    }
    
    if ($admin_type === 'company' && !isCompanyAdmin() && !isMasterAdmin()) {
        header('Location: ../../dashboard.php');
        exit();
    }
}
?>
