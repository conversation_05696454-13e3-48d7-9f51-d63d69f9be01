<?php
/**
 * Master Admin Dashboard
 * Comprehensive system administration interface for software owner
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../classes/User.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();
$user = new User($db);

// Get system statistics
$stats = [];

// Total companies
$query = "SELECT COUNT(*) as total FROM companies WHERE status = 'active'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_companies'] = $stmt->fetch()['total'];

// Total users
$query = "SELECT COUNT(*) as total FROM users WHERE status = 'active'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_users'] = $stmt->fetch()['total'];

// Total payments this month
$query = "SELECT COUNT(*) as total, SUM(amount) as amount FROM payments
          WHERE payment_status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE())";
$stmt = $db->prepare($query);
$stmt->execute();
$payment_stats = $stmt->fetch();
$stats['monthly_payments'] = $payment_stats['total'] ?? 0;
$stats['monthly_revenue'] = $payment_stats['amount'] ?? 0;

// Recent activity
$query = "SELECT al.*, u.name as user_name, c.company_name
          FROM activity_logs al
          LEFT JOIN users u ON al.user_id = u.id
          LEFT JOIN companies c ON al.company_id = c.id
          ORDER BY al.created_at DESC LIMIT 10";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_activities = $stmt->fetchAll();

// Recent companies
$query = "SELECT * FROM companies ORDER BY created_at DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_companies = $stmt->fetchAll();

// Recent users
$query = "SELECT u.*, c.company_name FROM users u
          LEFT JOIN companies c ON u.company_id = c.id
          ORDER BY u.created_at DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_users = $stmt->fetchAll();

$page_title = "Master Admin Dashboard";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'dashboard.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Master Admin Dashboard</h1>
            <p class="page-subtitle">Comprehensive system administration and oversight</p>
        </div>
        
        <!-- System Statistics -->
        <div class="admin-stats">
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['total_companies']); ?></div>
                <div class="stat-label">Active Companies</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['total_users']); ?></div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['monthly_payments']); ?></div>
                <div class="stat-label">Monthly Payments</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">R<?php echo number_format($stats['monthly_revenue'], 2); ?></div>
                <div class="stat-label">Monthly Revenue</div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="admin-actions">
            <a href="company_registration.php" class="admin-action-btn">
                🏢 Register New Company
            </a>
            <a href="users.php?action=create" class="admin-action-btn">
                👤 Create User
            </a>
            <a href="payments.php" class="admin-action-btn">
                💳 View All Payments
            </a>
            <a href="activity.php" class="admin-action-btn">
                📊 System Activity
            </a>
            <a href="settings.php" class="admin-action-btn">
                ⚙️ System Settings
            </a>
            <a href="reports.php" class="admin-action-btn">
                📈 Generate Reports
            </a>
        </div>

        <!-- Recent Activity and Management Sections -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin: 2rem 0;">
            <!-- Recent Companies -->
            <div class="admin-card">
                <h3 style="margin-bottom: 1.5rem; color: #2B5E5F;">Recent Companies</h3>
                <div class="company-list">
                    <?php foreach ($recent_companies as $company): ?>
                        <div style="padding: 1rem; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600;"><?php echo htmlspecialchars($company['company_name']); ?></div>
                                <div style="font-size: 0.85rem; color: #666;">Code: <?php echo htmlspecialchars($company['company_code']); ?></div>
                                <div style="font-size: 0.8rem; color: #999;">
                                    <?php echo date('M j, Y', strtotime($company['created_at'])); ?>
                                </div>
                            </div>
                            <div>
                                <a href="companies.php?id=<?php echo $company['id']; ?>" class="btn btn-sm">View</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div style="margin-top: 1rem; text-align: center;">
                    <a href="companies.php" class="btn">View All Companies</a>
                </div>
            </div>

            <!-- Recent Users -->
            <div class="admin-card">
                <h3 style="margin-bottom: 1.5rem; color: #2B5E5F;">Recent Users</h3>
                <div class="user-list">
                    <?php foreach ($recent_users as $user_item): ?>
                        <div style="padding: 1rem; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600;"><?php echo htmlspecialchars($user_item['name']); ?></div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($user_item['email']); ?></div>
                                <div style="font-size: 0.8rem; color: #999;">
                                    <?php echo htmlspecialchars($user_item['company_name'] ?? 'No Company'); ?> •
                                    <?php echo ucfirst($user_item['role']); ?>
                                </div>
                            </div>
                            <div>
                                <a href="users.php?id=<?php echo $user_item['id']; ?>" class="btn btn-sm">View</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div style="margin-top: 1rem; text-align: center;">
                    <a href="users.php" class="btn">View All Users</a>
                </div>
            </div>
        </div>

        <!-- Recent Activity Log -->
        <div class="admin-card">
            <h3 style="margin-bottom: 1.5rem; color: #2B5E5F;">Recent System Activity</h3>
            <div class="activity-log">
                <?php foreach ($recent_activities as $activity): ?>
                    <div style="padding: 1rem; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                        <div style="flex: 1;">
                            <div style="font-weight: 600; margin-bottom: 0.25rem;">
                                <?php
                                $action_icons = [
                                    'login' => '🔐',
                                    'logout' => '🚪',
                                    'company_created' => '🏢',
                                    'user_created' => '👤',
                                    'user_updated' => '✏️',
                                    'payment_made' => '💳',
                                    'invoice_downloaded' => '📥'
                                ];
                                echo $action_icons[$activity['action_type']] ?? '📝';
                                ?>
                                <?php echo htmlspecialchars($activity['description']); ?>
                            </div>
                            <div style="font-size: 0.8rem; color: #666;">
                                <?php if ($activity['user_name']): ?>
                                    by <?php echo htmlspecialchars($activity['user_name']); ?>
                                <?php endif; ?>
                                <?php if ($activity['company_name']): ?>
                                    (<?php echo htmlspecialchars($activity['company_name']); ?>)
                                <?php endif; ?>
                            </div>
                        </div>
                        <div style="font-size: 0.8rem; color: #999; text-align: right;">
                            <?php echo date('M j, Y H:i', strtotime($activity['created_at'])); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div style="margin-top: 1rem; text-align: center;">
                <a href="activity.php" class="btn">View Full Activity Log</a>
            </div>
        </div>
    </main>
</body>
</html>
