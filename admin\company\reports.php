<?php
/**
 * Company Reports
 * Company Admin interface for generating company-specific reports
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and company admin privileges
requireLogin();
requireAdminAccess('company');

$database = new Database();
$db = $database->getConnection();

// Get current user's company ID
$company_id = $_SESSION['company_id'];

// Get date range for reports
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // Today
$report_type = $_GET['report_type'] ?? 'overview';

// Generate reports based on type
$report_data = [];

switch ($report_type) {
    case 'overview':
        // Company overview statistics
        $queries = [
            'total_users' => "SELECT COUNT(*) FROM users WHERE company_id = ? AND status = 'active'",
            'total_payments' => "SELECT COUNT(*) FROM payments WHERE company_id = ? AND DATE(created_at) BETWEEN ? AND ?",
            'total_revenue' => "SELECT COALESCE(SUM(amount), 0) FROM payments WHERE company_id = ? AND payment_status = 'completed' AND DATE(created_at) BETWEEN ? AND ?",
            'pending_payments' => "SELECT COUNT(*) FROM payments WHERE company_id = ? AND payment_status = 'pending' AND DATE(created_at) BETWEEN ? AND ?",
            'failed_payments' => "SELECT COUNT(*) FROM payments WHERE company_id = ? AND payment_status = 'failed' AND DATE(created_at) BETWEEN ? AND ?",
            'new_users' => "SELECT COUNT(*) FROM users WHERE company_id = ? AND DATE(created_at) BETWEEN ? AND ?",
            'avg_payment' => "SELECT COALESCE(AVG(amount), 0) FROM payments WHERE company_id = ? AND payment_status = 'completed' AND DATE(created_at) BETWEEN ? AND ?"
        ];
        
        foreach ($queries as $key => $query) {
            $stmt = $db->prepare($query);
            if ($key === 'total_users') {
                $stmt->execute([$company_id]);
            } else {
                $stmt->execute([$company_id, $date_from, $date_to]);
            }
            $report_data[$key] = $stmt->fetchColumn();
        }
        break;
        
    case 'users':
        // User activity report for company
        $query = "SELECT u.name, u.email, u.role,
                         u.created_at, u.last_login,
                         COUNT(DISTINCT al.id) as activity_count,
                         COUNT(DISTINCT p.id) as payment_count,
                         COALESCE(SUM(CASE WHEN p.payment_status = 'completed' THEN p.amount ELSE 0 END), 0) as total_spent
                  FROM users u
                  LEFT JOIN activity_logs al ON u.id = al.user_id AND DATE(al.created_at) BETWEEN ? AND ?
                  LEFT JOIN payments p ON u.id = p.user_id AND DATE(p.created_at) BETWEEN ? AND ?
                  WHERE u.company_id = ? AND u.status = 'active'
                  GROUP BY u.id, u.name, u.email, u.role, u.created_at, u.last_login
                  ORDER BY activity_count DESC";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$date_from, $date_to, $date_from, $date_to, $company_id]);
        $report_data['users'] = $stmt->fetchAll();
        break;
        
    case 'payments':
        // Payment analysis report for company
        $query = "SELECT DATE(p.created_at) as payment_date,
                         COUNT(*) as transaction_count,
                         SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as completed_amount,
                         SUM(CASE WHEN payment_status = 'pending' THEN amount ELSE 0 END) as pending_amount,
                         SUM(CASE WHEN payment_status = 'failed' THEN amount ELSE 0 END) as failed_amount,
                         COUNT(CASE WHEN payment_status = 'completed' THEN 1 END) as completed_count,
                         COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_count,
                         COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_count
                  FROM payments p
                  WHERE p.company_id = ? AND DATE(p.created_at) BETWEEN ? AND ?
                  GROUP BY DATE(p.created_at)
                  ORDER BY payment_date DESC";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$company_id, $date_from, $date_to]);
        $report_data['payments'] = $stmt->fetchAll();
        break;
        
    case 'activity':
        // Activity logs for company
        $query = "SELECT al.action_type, al.description, al.created_at,
                         u.name as user_name, u.email as user_email
                  FROM activity_logs al
                  LEFT JOIN users u ON al.user_id = u.id
                  WHERE al.company_id = ? AND DATE(al.created_at) BETWEEN ? AND ?
                  ORDER BY al.created_at DESC
                  LIMIT 100";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$company_id, $date_from, $date_to]);
        $report_data['activity'] = $stmt->fetchAll();
        break;
}

// Get company info
$company_query = "SELECT company_name FROM companies WHERE id = ?";
$company_stmt = $db->prepare($company_query);
$company_stmt->execute([$company_id]);
$company_info = $company_stmt->fetch();

$page_title = "Company Reports";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('company'); ?>
    <style>
        .report-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: end;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .filter-label {
            font-weight: 600;
            color: #2B5E5F;
            font-size: 0.9rem;
        }
        
        .filter-input, .filter-select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }
        
        .report-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #2B5E5F;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .report-table th {
            background: #2B5E5F;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .report-table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .report-table tr:hover {
            background: #f8fafa;
        }
        
        .export-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .amount {
            font-weight: 600;
        }
        
        .amount.positive {
            color: #065f46;
        }
        
        .amount.negative {
            color: #991b1b;
        }
        
        .no-data {
            text-align: center;
            padding: 3rem;
            color: #666;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .action-type {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .action-login { background: #d1fae5; color: #065f46; }
        .action-logout { background: #fee2e2; color: #991b1b; }
        .action-payment_made { background: #d1fae5; color: #065f46; }
        .action-user_created { background: #fef3c7; color: #92400e; }
        .action-user_updated { background: #e0e7ff; color: #3730a3; }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'company', 'reports.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Company Reports</h1>
            <p class="page-subtitle">Analytics and reports for <?php echo htmlspecialchars($company_info['company_name']); ?></p>
        </div>
        
        <!-- Report Filters -->
        <form method="GET" class="report-filters">
            <div class="filter-group">
                <label class="filter-label">Report Type</label>
                <select name="report_type" class="filter-select" onchange="this.form.submit()">
                    <option value="overview" <?php echo $report_type === 'overview' ? 'selected' : ''; ?>>Company Overview</option>
                    <option value="users" <?php echo $report_type === 'users' ? 'selected' : ''; ?>>User Activity</option>
                    <option value="payments" <?php echo $report_type === 'payments' ? 'selected' : ''; ?>>Payment Analysis</option>
                    <option value="activity" <?php echo $report_type === 'activity' ? 'selected' : ''; ?>>Activity Logs</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">Date From</label>
                <input type="date" name="date_from" class="filter-input" value="<?php echo htmlspecialchars($date_from); ?>">
            </div>
            
            <div class="filter-group">
                <label class="filter-label">Date To</label>
                <input type="date" name="date_to" class="filter-input" value="<?php echo htmlspecialchars($date_to); ?>">
            </div>
            
            <div class="filter-group">
                <button type="submit" class="btn">Generate Report</button>
            </div>
        </form>
        
        <!-- Export Buttons -->
        <div class="export-buttons">
            <button onclick="exportReport('pdf')" class="btn btn-secondary">Export PDF</button>
            <button onclick="exportReport('excel')" class="btn btn-secondary">Export Excel</button>
            <button onclick="exportReport('csv')" class="btn btn-secondary">Export CSV</button>
            <button onclick="window.print()" class="btn btn-secondary">Print Report</button>
        </div>
        
        <?php if ($report_type === 'overview'): ?>
            <!-- Company Overview Report -->
            <div class="report-stats">
                <div class="stat-card">
                    <div class="stat-value"><?php echo number_format($report_data['total_users']); ?></div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value"><?php echo number_format($report_data['total_payments']); ?></div>
                    <div class="stat-label">Total Payments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">R<?php echo number_format($report_data['total_revenue'], 2); ?></div>
                    <div class="stat-label">Total Revenue</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">R<?php echo number_format($report_data['avg_payment'], 2); ?></div>
                    <div class="stat-label">Average Payment</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value"><?php echo number_format($report_data['new_users']); ?></div>
                    <div class="stat-label">New Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value"><?php echo number_format($report_data['pending_payments']); ?></div>
                    <div class="stat-label">Pending Payments</div>
                </div>
            </div>
            
        <?php elseif ($report_type === 'users'): ?>
            <!-- User Activity Report -->
            <div class="admin-card">
                <h3 style="margin-bottom: 1.5rem; color: #2B5E5F;">User Activity Report</h3>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Role</th>
                            <th>Activities</th>
                            <th>Payments</th>
                            <th>Total Spent</th>
                            <th>Last Login</th>
                            <th>Joined</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($report_data['users'] as $user): ?>
                            <tr>
                                <td>
                                    <div style="font-weight: 600;"><?php echo htmlspecialchars($user['name']); ?></div>
                                    <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($user['email']); ?></div>
                                </td>
                                <td>
                                    <span style="text-transform: capitalize;">
                                        <?php echo str_replace('_', ' ', $user['role']); ?>
                                    </span>
                                </td>
                                <td><?php echo number_format($user['activity_count']); ?></td>
                                <td><?php echo number_format($user['payment_count']); ?></td>
                                <td>
                                    <span class="amount positive">R<?php echo number_format($user['total_spent'], 2); ?></span>
                                </td>
                                <td>
                                    <?php if ($user['last_login']): ?>
                                        <?php echo date('M j, Y H:i', strtotime($user['last_login'])); ?>
                                    <?php else: ?>
                                        <span style="color: #999;">Never</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <?php if (empty($report_data['users'])): ?>
                    <div class="no-data">
                        <p>No user data found for the selected date range.</p>
                    </div>
                <?php endif; ?>
            </div>
            
        <?php elseif ($report_type === 'payments'): ?>
            <!-- Payment Analysis Report -->
            <div class="admin-card">
                <h3 style="margin-bottom: 1.5rem; color: #2B5E5F;">Payment Analysis Report</h3>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Total Transactions</th>
                            <th>Completed</th>
                            <th>Pending</th>
                            <th>Failed</th>
                            <th>Total Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($report_data['payments'] as $payment): ?>
                            <tr>
                                <td><?php echo date('M j, Y', strtotime($payment['payment_date'])); ?></td>
                                <td><?php echo number_format($payment['transaction_count']); ?></td>
                                <td>
                                    <div><?php echo number_format($payment['completed_count']); ?> transactions</div>
                                    <div style="font-size: 0.85rem; color: #065f46;">
                                        R<?php echo number_format($payment['completed_amount'], 2); ?>
                                    </div>
                                </td>
                                <td>
                                    <div><?php echo number_format($payment['pending_count']); ?> transactions</div>
                                    <div style="font-size: 0.85rem; color: #92400e;">
                                        R<?php echo number_format($payment['pending_amount'], 2); ?>
                                    </div>
                                </td>
                                <td>
                                    <div><?php echo number_format($payment['failed_count']); ?> transactions</div>
                                    <div style="font-size: 0.85rem; color: #991b1b;">
                                        R<?php echo number_format($payment['failed_amount'], 2); ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="amount positive">
                                        R<?php echo number_format($payment['completed_amount'] + $payment['pending_amount'] + $payment['failed_amount'], 2); ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <?php if (empty($report_data['payments'])): ?>
                    <div class="no-data">
                        <p>No payment data found for the selected date range.</p>
                    </div>
                <?php endif; ?>
            </div>
            
        <?php elseif ($report_type === 'activity'): ?>
            <!-- Activity Logs Report -->
            <div class="admin-card">
                <h3 style="margin-bottom: 1.5rem; color: #2B5E5F;">Recent Activity Logs</h3>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>Action</th>
                            <th>Description</th>
                            <th>User</th>
                            <th>Date/Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($report_data['activity'] as $activity): ?>
                            <tr>
                                <td>
                                    <span class="action-type action-<?php echo $activity['action_type']; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $activity['action_type'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($activity['description']); ?>
                                </td>
                                <td>
                                    <?php if ($activity['user_name']): ?>
                                        <div style="font-weight: 600;"><?php echo htmlspecialchars($activity['user_name']); ?></div>
                                        <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($activity['user_email']); ?></div>
                                    <?php else: ?>
                                        <span style="color: #999;">System</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div><?php echo date('M j, Y', strtotime($activity['created_at'])); ?></div>
                                    <div style="font-size: 0.85rem; color: #666;"><?php echo date('H:i:s', strtotime($activity['created_at'])); ?></div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <?php if (empty($report_data['activity'])): ?>
                    <div class="no-data">
                        <p>No activity logs found for the selected date range.</p>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </main>
    
    <script>
        function exportReport(format) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', format);
            
            // Create a temporary link to download the export
            const link = document.createElement('a');
            link.href = 'export_report.php?' + params.toString();
            link.download = `company_report_${format}_${new Date().toISOString().split('T')[0]}.${format}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
