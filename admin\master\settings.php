<?php
/**
 * System Settings
 * Master Admin interface for managing system-wide settings
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_general':
                // Update general settings
                $app_name = trim($_POST['app_name'] ?? '');
                $app_description = trim($_POST['app_description'] ?? '');
                $admin_email = trim($_POST['admin_email'] ?? '');
                $timezone = $_POST['timezone'] ?? 'UTC';
                
                if (empty($app_name) || empty($admin_email)) {
                    $error_message = 'App name and admin email are required.';
                } elseif (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
                    $error_message = 'Please enter a valid admin email address.';
                } else {
                    // Update settings in database or config file
                    // For now, we'll just show success message
                    $success_message = 'General settings updated successfully.';
                    logActivity('settings_updated', 'General settings updated', $_SESSION['user_id']);
                }
                break;
                
            case 'update_security':
                // Update security settings
                $session_timeout = intval($_POST['session_timeout'] ?? 30);
                $password_min_length = intval($_POST['password_min_length'] ?? 8);
                $max_login_attempts = intval($_POST['max_login_attempts'] ?? 5);
                $require_2fa = isset($_POST['require_2fa']);
                
                if ($session_timeout < 5 || $session_timeout > 1440) {
                    $error_message = 'Session timeout must be between 5 and 1440 minutes.';
                } elseif ($password_min_length < 6 || $password_min_length > 50) {
                    $error_message = 'Password minimum length must be between 6 and 50 characters.';
                } else {
                    $success_message = 'Security settings updated successfully.';
                    logActivity('settings_updated', 'Security settings updated', $_SESSION['user_id']);
                }
                break;
                
            case 'update_payment':
                // Update payment settings
                $payment_gateway = $_POST['payment_gateway'] ?? 'payfast';
                $currency = $_POST['currency'] ?? 'ZAR';
                $tax_rate = floatval($_POST['tax_rate'] ?? 15);
                
                if ($tax_rate < 0 || $tax_rate > 100) {
                    $error_message = 'Tax rate must be between 0 and 100 percent.';
                } else {
                    $success_message = 'Payment settings updated successfully.';
                    logActivity('settings_updated', 'Payment settings updated', $_SESSION['user_id']);
                }
                break;
                
            case 'update_email':
                // Update email settings
                $smtp_host = trim($_POST['smtp_host'] ?? '');
                $smtp_port = intval($_POST['smtp_port'] ?? 587);
                $smtp_username = trim($_POST['smtp_username'] ?? '');
                $smtp_password = trim($_POST['smtp_password'] ?? '');
                $from_email = trim($_POST['from_email'] ?? '');
                $from_name = trim($_POST['from_name'] ?? '');
                
                if (empty($smtp_host) || empty($from_email)) {
                    $error_message = 'SMTP host and from email are required.';
                } elseif (!filter_var($from_email, FILTER_VALIDATE_EMAIL)) {
                    $error_message = 'Please enter a valid from email address.';
                } else {
                    $success_message = 'Email settings updated successfully.';
                    logActivity('settings_updated', 'Email settings updated', $_SESSION['user_id']);
                }
                break;
        }
    }
}

// Get current settings (these would normally come from database or config)
$current_settings = [
    'app_name' => APP_NAME,
    'app_description' => 'Multi-tenant Import/Export Clearing Portal',
    'admin_email' => '<EMAIL>',
    'timezone' => 'Africa/Johannesburg',
    'session_timeout' => 30,
    'password_min_length' => 8,
    'max_login_attempts' => 5,
    'require_2fa' => false,
    'payment_gateway' => 'payfast',
    'currency' => 'ZAR',
    'tax_rate' => 15.0,
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => 587,
    'smtp_username' => '',
    'smtp_password' => '',
    'from_email' => '<EMAIL>',
    'from_name' => APP_NAME
];

$csrf_token = generateCsrfToken();
$page_title = "System Settings";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .settings-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0;
        }
        
        .tab-button {
            padding: 1rem 2rem;
            background: none;
            border: none;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            color: #2B5E5F;
            border-bottom-color: #2B5E5F;
        }
        
        .tab-button:hover {
            color: #2B5E5F;
            background: #f8fafa;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .settings-form {
            display: grid;
            gap: 2rem;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #2B5E5F;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #374151;
        }
        
        .form-input, .form-select, .form-textarea {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #2B5E5F;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .form-checkbox input {
            width: 1.2rem;
            height: 1.2rem;
        }
        
        .form-help {
            font-size: 0.85rem;
            color: #666;
            margin-top: 0.25rem;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #f0f0f0;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'settings.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">System Settings</h1>
            <p class="page-subtitle">Configure system-wide settings and preferences</p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="admin-card">
            <!-- Settings Tabs -->
            <div class="settings-tabs">
                <button class="tab-button active" onclick="showTab('general')">General</button>
                <button class="tab-button" onclick="showTab('security')">Security</button>
                <button class="tab-button" onclick="showTab('payment')">Payment</button>
                <button class="tab-button" onclick="showTab('email')">Email</button>
            </div>
            
            <!-- General Settings Tab -->
            <div id="general-tab" class="tab-content active">
                <form method="POST" class="settings-form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="update_general">
                    
                    <div class="form-section">
                        <h3 class="section-title">Application Settings</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">Application Name</label>
                                <input type="text" name="app_name" class="form-input" 
                                       value="<?php echo htmlspecialchars($current_settings['app_name']); ?>" required>
                                <div class="form-help">The name of your application</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Admin Email</label>
                                <input type="email" name="admin_email" class="form-input" 
                                       value="<?php echo htmlspecialchars($current_settings['admin_email']); ?>" required>
                                <div class="form-help">Primary administrator email address</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Timezone</label>
                                <select name="timezone" class="form-select">
                                    <option value="UTC" <?php echo $current_settings['timezone'] === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                    <option value="Africa/Johannesburg" <?php echo $current_settings['timezone'] === 'Africa/Johannesburg' ? 'selected' : ''; ?>>Africa/Johannesburg</option>
                                    <option value="America/New_York" <?php echo $current_settings['timezone'] === 'America/New_York' ? 'selected' : ''; ?>>America/New_York</option>
                                    <option value="Europe/London" <?php echo $current_settings['timezone'] === 'Europe/London' ? 'selected' : ''; ?>>Europe/London</option>
                                </select>
                                <div class="form-help">Default timezone for the application</div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Application Description</label>
                            <textarea name="app_description" class="form-textarea"><?php echo htmlspecialchars($current_settings['app_description']); ?></textarea>
                            <div class="form-help">Brief description of your application</div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">Save General Settings</button>
                    </div>
                </form>
            </div>
            
            <!-- Security Settings Tab -->
            <div id="security-tab" class="tab-content">
                <form method="POST" class="settings-form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="update_security">
                    
                    <div class="form-section">
                        <h3 class="section-title">Security Configuration</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">Session Timeout (minutes)</label>
                                <input type="number" name="session_timeout" class="form-input" 
                                       value="<?php echo $current_settings['session_timeout']; ?>" min="5" max="1440" required>
                                <div class="form-help">How long users stay logged in (5-1440 minutes)</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Password Minimum Length</label>
                                <input type="number" name="password_min_length" class="form-input" 
                                       value="<?php echo $current_settings['password_min_length']; ?>" min="6" max="50" required>
                                <div class="form-help">Minimum characters required for passwords</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Max Login Attempts</label>
                                <input type="number" name="max_login_attempts" class="form-input" 
                                       value="<?php echo $current_settings['max_login_attempts']; ?>" min="3" max="10" required>
                                <div class="form-help">Maximum failed login attempts before lockout</div>
                            </div>
                        </div>
                        
                        <div class="form-checkbox">
                            <input type="checkbox" name="require_2fa" id="require_2fa" 
                                   <?php echo $current_settings['require_2fa'] ? 'checked' : ''; ?>>
                            <label for="require_2fa" class="form-label">Require Two-Factor Authentication</label>
                        </div>
                        <div class="form-help">Force all users to enable 2FA for enhanced security</div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">Save Security Settings</button>
                    </div>
                </form>
            </div>
            
            <!-- Payment Settings Tab -->
            <div id="payment-tab" class="tab-content">
                <form method="POST" class="settings-form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="update_payment">
                    
                    <div class="form-section">
                        <h3 class="section-title">Payment Configuration</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">Payment Gateway</label>
                                <select name="payment_gateway" class="form-select">
                                    <option value="payfast" <?php echo $current_settings['payment_gateway'] === 'payfast' ? 'selected' : ''; ?>>PayFast</option>
                                    <option value="peach" <?php echo $current_settings['payment_gateway'] === 'peach' ? 'selected' : ''; ?>>Peach Payments</option>
                                    <option value="paygate" <?php echo $current_settings['payment_gateway'] === 'paygate' ? 'selected' : ''; ?>>PayGate</option>
                                </select>
                                <div class="form-help">Primary payment gateway for processing payments</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Currency</label>
                                <select name="currency" class="form-select">
                                    <option value="ZAR" <?php echo $current_settings['currency'] === 'ZAR' ? 'selected' : ''; ?>>ZAR (South African Rand)</option>
                                    <option value="USD" <?php echo $current_settings['currency'] === 'USD' ? 'selected' : ''; ?>>USD (US Dollar)</option>
                                    <option value="EUR" <?php echo $current_settings['currency'] === 'EUR' ? 'selected' : ''; ?>>EUR (Euro)</option>
                                    <option value="GBP" <?php echo $current_settings['currency'] === 'GBP' ? 'selected' : ''; ?>>GBP (British Pound)</option>
                                </select>
                                <div class="form-help">Default currency for all transactions</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Tax Rate (%)</label>
                                <input type="number" name="tax_rate" class="form-input" step="0.01" 
                                       value="<?php echo $current_settings['tax_rate']; ?>" min="0" max="100" required>
                                <div class="form-help">Default tax rate applied to transactions</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">Save Payment Settings</button>
                    </div>
                </form>
            </div>
            
            <!-- Email Settings Tab -->
            <div id="email-tab" class="tab-content">
                <form method="POST" class="settings-form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="update_email">
                    
                    <div class="form-section">
                        <h3 class="section-title">SMTP Configuration</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">SMTP Host</label>
                                <input type="text" name="smtp_host" class="form-input" 
                                       value="<?php echo htmlspecialchars($current_settings['smtp_host']); ?>" required>
                                <div class="form-help">SMTP server hostname</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">SMTP Port</label>
                                <input type="number" name="smtp_port" class="form-input" 
                                       value="<?php echo $current_settings['smtp_port']; ?>" min="1" max="65535" required>
                                <div class="form-help">SMTP server port (usually 587 or 465)</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">SMTP Username</label>
                                <input type="text" name="smtp_username" class="form-input" 
                                       value="<?php echo htmlspecialchars($current_settings['smtp_username']); ?>">
                                <div class="form-help">SMTP authentication username</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">SMTP Password</label>
                                <input type="password" name="smtp_password" class="form-input" 
                                       value="<?php echo htmlspecialchars($current_settings['smtp_password']); ?>">
                                <div class="form-help">SMTP authentication password</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">From Email</label>
                                <input type="email" name="from_email" class="form-input" 
                                       value="<?php echo htmlspecialchars($current_settings['from_email']); ?>" required>
                                <div class="form-help">Email address used as sender</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">From Name</label>
                                <input type="text" name="from_name" class="form-input" 
                                       value="<?php echo htmlspecialchars($current_settings['from_name']); ?>">
                                <div class="form-help">Name displayed as sender</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">Save Email Settings</button>
                        <button type="button" class="btn btn-secondary" onclick="testEmail()">Test Email</button>
                    </div>
                </form>
            </div>
        </div>
    </main>
    
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // Add active class to clicked tab button
            event.target.classList.add('active');
        }
        
        function testEmail() {
            alert('Email test functionality would be implemented here.');
        }
    </script>
</body>
</html>
