<?php
/**
 * Master Admin Module Management
 * Control which modules users can access
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../classes/User.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

// Handle form submissions
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = 'Invalid CSRF token';
        $message_type = 'error';
    } else {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_user_modules':
                    $user_id = $_POST['user_id'];
                    $modules = $_POST['modules'] ?? [];
                    
                    // Delete existing module permissions
                    $query = "DELETE FROM user_modules WHERE user_id = ?";
                    $stmt = $db->prepare($query);
                    $stmt->execute([$user_id]);
                    
                    // Insert new module permissions
                    if (!empty($modules)) {
                        $query = "INSERT INTO user_modules (user_id, module_name, granted_at, granted_by) VALUES (?, ?, NOW(), ?)";
                        $stmt = $db->prepare($query);
                        foreach ($modules as $module) {
                            $stmt->execute([$user_id, $module, $_SESSION['user_id']]);
                        }
                    }
                    
                    $message = 'Module permissions updated successfully';
                    $message_type = 'success';
                    break;
                    
                case 'update_company_modules':
                    $company_id = $_POST['company_id'];
                    $modules = $_POST['modules'] ?? [];
                    
                    // Delete existing company module permissions
                    $query = "DELETE FROM company_modules WHERE company_id = ?";
                    $stmt = $db->prepare($query);
                    $stmt->execute([$company_id]);
                    
                    // Insert new company module permissions
                    if (!empty($modules)) {
                        $query = "INSERT INTO company_modules (company_id, module_name, granted_at, granted_by) VALUES (?, ?, NOW(), ?)";
                        $stmt = $db->prepare($query);
                        foreach ($modules as $module) {
                            $stmt->execute([$company_id, $module, $_SESSION['user_id']]);
                        }
                    }
                    
                    $message = 'Company module permissions updated successfully';
                    $message_type = 'success';
                    break;
            }
        }
    }
}

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Get all available modules
$available_modules = [
    'invoices' => 'Commercial Invoices',
    'freight' => 'Freight Management', 
    'tariff' => 'Customs Tariff',
    'accounting' => 'Trade Finance',
    'backoffice' => 'Back Office'
];

// Get all companies
$query = "SELECT id, company_name, company_code FROM companies WHERE status = 'active' ORDER BY company_name";
$stmt = $db->prepare($query);
$stmt->execute();
$companies = $stmt->fetchAll();

// Get all users
$query = "SELECT u.id, u.name, u.email, u.role, c.company_name 
          FROM users u 
          LEFT JOIN companies c ON u.company_id = c.id 
          WHERE u.status = 'active' AND u.role != 'master_admin'
          ORDER BY u.name";
$stmt = $db->prepare($query);
$stmt->execute();
$users = $stmt->fetchAll();

// Get current module permissions for companies
$company_modules = [];
$query = "SELECT company_id, module_name FROM company_modules";
$stmt = $db->prepare($query);
$stmt->execute();
$results = $stmt->fetchAll();
foreach ($results as $row) {
    $company_modules[$row['company_id']][] = $row['module_name'];
}

// Get current module permissions for users
$user_modules = [];
$query = "SELECT user_id, module_name FROM user_modules";
$stmt = $db->prepare($query);
$stmt->execute();
$results = $stmt->fetchAll();
foreach ($results as $row) {
    $user_modules[$row['user_id']][] = $row['module_name'];
}

echo generateAdminHeader('Module Management', 'master');
?>

<div class="admin-content">
    <div class="content-header">
        <div class="header-left">
            <h1>Module Management</h1>
            <p>Control which modules users and companies can access</p>
        </div>
    </div>

    <?php if ($message): ?>
    <div class="alert alert-<?php echo $message_type; ?>">
        <?php echo htmlspecialchars($message); ?>
    </div>
    <?php endif; ?>

    <div class="admin-tabs">
        <button class="tab-button active" onclick="showTab('company-modules')">Company Modules</button>
        <button class="tab-button" onclick="showTab('user-modules')">User Modules</button>
        <button class="tab-button" onclick="showTab('module-overview')">Module Overview</button>
    </div>

    <!-- Company Modules Tab -->
    <div id="company-modules" class="tab-content active">
        <div class="card">
            <div class="card-header">
                <h3>Company Module Permissions</h3>
                <p>Set which modules each company can access</p>
            </div>
            <div class="card-body">
                <?php foreach ($companies as $company): ?>
                <div class="module-permission-card">
                    <div class="permission-header">
                        <h4><?php echo htmlspecialchars($company['company_name']); ?></h4>
                        <span class="company-code"><?php echo htmlspecialchars($company['company_code']); ?></span>
                    </div>
                    <form method="POST" class="permission-form">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <input type="hidden" name="action" value="update_company_modules">
                        <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                        
                        <div class="module-checkboxes">
                            <?php foreach ($available_modules as $module_key => $module_name): ?>
                            <label class="module-checkbox">
                                <input type="checkbox" 
                                       name="modules[]" 
                                       value="<?php echo $module_key; ?>"
                                       <?php echo (isset($company_modules[$company['id']]) && in_array($module_key, $company_modules[$company['id']])) ? 'checked' : ''; ?>>
                                <span class="checkmark"></span>
                                <?php echo htmlspecialchars($module_name); ?>
                            </label>
                            <?php endforeach; ?>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Update Permissions</button>
                    </form>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- User Modules Tab -->
    <div id="user-modules" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h3>Individual User Module Permissions</h3>
                <p>Override company permissions for specific users</p>
            </div>
            <div class="card-body">
                <?php foreach ($users as $user): ?>
                <div class="module-permission-card">
                    <div class="permission-header">
                        <h4><?php echo htmlspecialchars($user['name']); ?></h4>
                        <div class="user-details">
                            <span class="user-email"><?php echo htmlspecialchars($user['email']); ?></span>
                            <span class="user-company"><?php echo htmlspecialchars($user['company_name'] ?? 'No Company'); ?></span>
                            <span class="user-role"><?php echo ucfirst($user['role']); ?></span>
                        </div>
                    </div>
                    <form method="POST" class="permission-form">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <input type="hidden" name="action" value="update_user_modules">
                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                        
                        <div class="module-checkboxes">
                            <?php foreach ($available_modules as $module_key => $module_name): ?>
                            <label class="module-checkbox">
                                <input type="checkbox" 
                                       name="modules[]" 
                                       value="<?php echo $module_key; ?>"
                                       <?php echo (isset($user_modules[$user['id']]) && in_array($module_key, $user_modules[$user['id']])) ? 'checked' : ''; ?>>
                                <span class="checkmark"></span>
                                <?php echo htmlspecialchars($module_name); ?>
                            </label>
                            <?php endforeach; ?>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Update Permissions</button>
                    </form>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Module Overview Tab -->
    <div id="module-overview" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h3>Module Access Overview</h3>
                <p>Summary of module access across the system</p>
            </div>
            <div class="card-body">
                <div class="overview-grid">
                    <?php foreach ($available_modules as $module_key => $module_name): ?>
                    <div class="module-overview-card">
                        <h4><?php echo htmlspecialchars($module_name); ?></h4>
                        
                        <?php
                        // Count companies with this module
                        $company_count = 0;
                        foreach ($company_modules as $modules) {
                            if (in_array($module_key, $modules)) $company_count++;
                        }
                        
                        // Count users with this module
                        $user_count = 0;
                        foreach ($user_modules as $modules) {
                            if (in_array($module_key, $modules)) $user_count++;
                        }
                        ?>
                        
                        <div class="overview-stats">
                            <div class="stat-item">
                                <span class="stat-number"><?php echo $company_count; ?></span>
                                <span class="stat-label">Companies</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number"><?php echo $user_count; ?></span>
                                <span class="stat-label">Individual Users</span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.admin-tabs {
    display: flex;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 2rem;
}

.tab-button {
    padding: 1rem 2rem;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: #6b7280;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: #2B5E5F;
}

.tab-button.active {
    color: #2B5E5F;
    border-bottom-color: #2B5E5F;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.module-permission-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.permission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.permission-header h4 {
    margin: 0;
    color: #1f2937;
}

.company-code {
    background: #f3f4f6;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #6b7280;
}

.user-details {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.user-email, .user-company, .user-role {
    font-size: 0.875rem;
    color: #6b7280;
}

.user-role {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.module-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.module-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.module-checkbox:hover {
    border-color: #2B5E5F;
    background: #f8fafc;
}

.module-checkbox input[type="checkbox"] {
    margin-right: 0.75rem;
    width: 1.25rem;
    height: 1.25rem;
    accent-color: #2B5E5F;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.module-overview-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
}

.module-overview-card h4 {
    margin: 0 0 1rem 0;
    color: #1f2937;
}

.overview-stats {
    display: flex;
    justify-content: space-around;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #2B5E5F;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.permission-form {
    margin: 0;
}

.btn {
    background: #2B5E5F;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s ease;
}

.btn:hover {
    background: #1e4a4b;
}

.alert {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
}

.alert-success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}
</style>

<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => content.classList.remove('active'));

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => button.classList.remove('active'));

    // Show selected tab content
    document.getElementById(tabName).classList.add('active');

    // Add active class to clicked button
    event.target.classList.add('active');
}
</script>

</div>
</body>
</html>
