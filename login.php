<?php
/**
 * FTP-Based Login Page
 * Secure login form with FTP authentication
 */

require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';
$success_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid request. Please try again.';
    } else {
        $email = sanitizeInput($_POST['email']);
        $password = $_POST['password'];
        $company_code = sanitizeInput($_POST['company_code']);

        // Validate input
        if (empty($email) || empty($password) || empty($company_code)) {
            $error_message = 'Please enter email address, password, and company code.';
        } else {
            // Include database connection and User class
            require_once 'config/database.php';
            require_once 'classes/User.php';

            try {
                $database = new Database();
                $db = $database->getConnection();
                $user = new User($db);

                // Attempt user login with company code
                if ($user->loginWithCompanyCode($email, $password, $company_code)) {
                    // Log successful login
                    logActivity('login', 'User logged in successfully', $_SESSION['user_id'], $_SESSION['company_id']);

                    header('Location: dashboard.php');
                    exit();
                } else {
                    $error_message = 'Invalid email address, password, or company code.';
                    // Log failed login attempt
                    logActivity('login', 'Failed login attempt for email: ' . $email . ' with company code: ' . $company_code);
                    error_log("Failed login attempt for email: " . $email . " with company code: " . $company_code . " from IP: " . $_SERVER['REMOTE_ADDR']);
                }
            } catch (Exception $e) {
                $error_message = 'Login system temporarily unavailable. Please try again later.';
                error_log("Login error: " . $e->getMessage());
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Advanced Customs Solutions</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, rgba(43, 94, 95, 0.95), rgba(30, 74, 75, 0.95));
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(43, 94, 95, 0.4) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(30, 74, 75, 0.4) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(43, 94, 95, 0.3) 0%, transparent 50%);
            animation: gradientShift 15s ease infinite;
        }

        @keyframes gradientShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.08),
                0 8px 16px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            overflow: hidden;
            width: 100%;
            max-width: 360px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            transform: translateY(0);
            transition: transform 0.3s ease;
        }

        .login-container:hover {
            transform: translateY(-4px);
            box-shadow:
                0 40px 80px rgba(0, 0, 0, 0.15),
                0 20px 40px rgba(43, 94, 95, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
        }

        .login-header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
            padding: 32px 24px 24px;
            text-align: center;
            position: relative;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #2B5E5F, #1e4a4b, #2B5E5F);
            background-size: 200% 100%;
            animation: gradientMove 3s ease infinite;
        }

        @keyframes gradientMove {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .logo-container {
            margin-bottom: 24px;
            position: relative;
        }

        .logo {
            width: 100px;
            height: auto;
            max-height: 70px;
            margin: 0 auto;
            display: block;
            filter: drop-shadow(0 3px 8px rgba(43, 94, 95, 0.15));
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .company-name {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .login-subtitle {
            font-size: 16px;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 6px;
        }

        .industry-tagline {
            font-size: 12px;
            color: #94a3b8;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            opacity: 0.8;
        }

        .login-form {
            padding: 24px;
            background: rgba(255, 255, 255, 0.95);
            position: relative;
        }

        .alert {
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 24px;
            font-size: 14px;
            font-weight: 500;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
        }

        .alert-error {
            background: linear-gradient(135deg, #fef2f2, #fde8e8);
            color: #dc2626;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
        }

        .alert-error::before {
            background: #dc2626;
        }

        .alert-success {
            background: linear-gradient(135deg, #f0fdf4, #e7f5e7);
            color: #16a34a;
            box-shadow: 0 4px 12px rgba(22, 163, 74, 0.1);
        }

        .alert-success::before {
            background: #16a34a;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 15px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
            color: #374151;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .form-group input:focus {
            outline: none;
            border-color: #2B5E5F;
            background: white;
            box-shadow:
                0 0 0 3px rgba(43, 94, 95, 0.1),
                0 4px 12px rgba(43, 94, 95, 0.15);
            transform: translateY(-1px);
        }

        .form-group input:hover {
            border-color: #d1d5db;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .form-group input::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }

        .btn {
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-top: 8px;
            box-shadow:
                0 3px 10px rgba(43, 94, 95, 0.4),
                0 2px 4px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            background: linear-gradient(135deg, #1e4a4b 0%, #163a3b 100%);
            transform: translateY(-2px);
            box-shadow:
                0 8px 20px rgba(43, 94, 95, 0.5),
                0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow:
                0 4px 12px rgba(43, 94, 95, 0.4),
                0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .login-footer {
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
            padding: 20px 24px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            color: #64748b;
            text-align: center;
            position: relative;
        }

        .demo-credentials {
            margin-top: 12px;
            padding: 16px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9));
            border-radius: 8px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            font-size: 13px;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.08);
            position: relative;
            overflow: hidden;
        }

        .demo-credentials::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #2B5E5F, #1e4a4b, #2B5E5F);
            background-size: 200% 100%;
            animation: gradientMove 3s ease infinite;
        }

        .demo-credentials p {
            margin: 6px 0;
            color: #475569;
            line-height: 1.5;
        }

        .demo-credentials strong {
            color: #1e293b;
            font-weight: 600;
        }

        .demo-credentials .credentials-title {
            color: #2B5E5F;
            font-weight: 700;
            margin-bottom: 12px;
            font-size: 14px;
        }

        /* Enhanced animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .login-container {
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Responsive design */
        @media (max-width: 480px) {
            .login-container {
                max-width: 100%;
                margin: 10px;
                border-radius: 12px;
            }

            .login-header {
                padding: 24px 20px 20px;
            }

            .login-form {
                padding: 20px;
            }

            .login-footer {
                padding: 16px 20px;
            }

            .demo-credentials {
                padding: 12px;
                margin-top: 10px;
            }

            .company-name {
                font-size: 20px;
            }

            .logo {
                width: 80px;
                max-height: 60px;
            }

            .form-group input {
                padding: 10px 14px;
                font-size: 14px;
            }

            .btn {
                padding: 10px 16px;
                font-size: 14px;
            }
        }

        /* Loading state for button */
        .btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .btn:disabled:hover {
            transform: none;
            box-shadow: 0 4px 12px rgba(43, 94, 95, 0.4);
        }

        /* Screen reader only content */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles for better accessibility */
        .form-group input:focus,
        .btn:focus {
            outline: 2px solid #2B5E5F;
            outline-offset: 2px;
        }

        /* Reduce motion for users who prefer it */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo-container">
                <img src="assets/img/logo.png" alt="ACS Logo" class="logo">
            </div>
            <div class="company-name">Advanced Customs Solutions</div>
            <div class="login-subtitle">Professional Import/Export Clearing Portal</div>
            <div class="industry-tagline">Streamlining Global Trade Operations</div>
        </div>

        <div class="login-form">
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="login.php" aria-label="Login form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                <div class="form-group">
                    <label for="email">Email Address:</label>
                    <input type="email" id="email" name="email" required
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                           placeholder="Enter your email address"
                           aria-describedby="email-help"
                           autocomplete="email">
                    <div id="email-help" class="sr-only">Enter the email address associated with your account</div>
                </div>

                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required
                           placeholder="Enter your password"
                           aria-describedby="password-help"
                           autocomplete="current-password">
                    <div id="password-help" class="sr-only">Enter your account password</div>
                </div>

                <div class="form-group">
                    <label for="company_code">Company Code:</label>
                    <input type="text" id="company_code" name="company_code" required
                           value="<?php echo isset($_POST['company_code']) ? htmlspecialchars($_POST['company_code']) : ''; ?>"
                           placeholder="Enter your company code"
                           style="text-transform: uppercase;"
                           aria-describedby="company-help"
                           autocomplete="organization">
                    <div id="company-help" class="sr-only">Enter the unique code provided by your company</div>
                </div>

                <button type="submit" class="btn">Log in</button>
            </form>
        </div>

        <div class="login-footer">
                </p>
            </div>
        </div>
    </div>
</body>
</html>
