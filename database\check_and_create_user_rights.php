<?php
/**
 * Database Check and User Rights Table Creation
 * This script checks if the user_rights table exists and creates it if needed
 */

require_once __DIR__ . '/../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "Connected to database successfully.\n";
    
    // Check if user_rights table exists
    $check_query = "SHOW TABLES LIKE 'user_rights'";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->execute();
    $table_exists = $check_stmt->fetch();
    
    if ($table_exists) {
        echo "user_rights table already exists.\n";
        
        // Check the structure
        $desc_query = "DESCRIBE user_rights";
        $desc_stmt = $db->prepare($desc_query);
        $desc_stmt->execute();
        $columns = $desc_stmt->fetchAll();
        
        echo "Current table structure:\n";
        foreach ($columns as $column) {
            echo "- {$column['Field']}: {$column['Type']}\n";
        }
        
    } else {
        echo "user_rights table does not exist. Creating it now...\n";
        
        // First, check the users table structure to see if it uses UUIDs or INTs
        $users_desc_query = "DESCRIBE users";
        $users_desc_stmt = $db->prepare($users_desc_query);
        $users_desc_stmt->execute();
        $users_columns = $users_desc_stmt->fetchAll();
        
        $user_id_type = 'INT(11)'; // default
        foreach ($users_columns as $column) {
            if ($column['Field'] === 'id') {
                $user_id_type = $column['Type'];
                break;
            }
        }
        
        echo "Users table ID type: {$user_id_type}\n";
        
        // Create user_rights table with appropriate data type
        if (strpos(strtolower($user_id_type), 'char') !== false || strpos(strtolower($user_id_type), 'varchar') !== false) {
            // UUID format
            $create_query = "
                CREATE TABLE user_rights (
                    id INT(11) NOT NULL AUTO_INCREMENT,
                    user_id VARCHAR(36) NOT NULL,
                    module_name VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    UNIQUE KEY unique_user_module (user_id, module_name),
                    KEY idx_user_id (user_id),
                    CONSTRAINT fk_user_rights_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
        } else {
            // INT format
            $create_query = "
                CREATE TABLE user_rights (
                    id INT(11) NOT NULL AUTO_INCREMENT,
                    user_id INT(11) NOT NULL,
                    module_name VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    UNIQUE KEY unique_user_module (user_id, module_name),
                    KEY idx_user_id (user_id),
                    CONSTRAINT fk_user_rights_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
        }
        
        $create_stmt = $db->prepare($create_query);
        $create_stmt->execute();
        
        echo "user_rights table created successfully!\n";
        
        // Insert some sample data for existing users
        echo "Adding sample module permissions...\n";
        
        // Get existing users
        $users_query = "SELECT id, role FROM users WHERE status != 'deleted' LIMIT 5";
        $users_stmt = $db->prepare($users_query);
        $users_stmt->execute();
        $users = $users_stmt->fetchAll();
        
        $modules = ['invoices', 'freight', 'tariff', 'accounting', 'backoffice'];
        
        foreach ($users as $user) {
            // Give different permissions based on role
            $user_modules = [];
            switch ($user['role']) {
                case 'master_admin':
                    $user_modules = $modules; // All modules
                    break;
                case 'company_admin':
                    $user_modules = $modules; // All modules
                    break;
                case 'user':
                    $user_modules = ['invoices', 'accounting']; // Limited modules
                    break;
            }
            
            foreach ($user_modules as $module) {
                $insert_query = "INSERT IGNORE INTO user_rights (user_id, module_name) VALUES (?, ?)";
                $insert_stmt = $db->prepare($insert_query);
                $insert_stmt->execute([$user['id'], $module]);
            }
            
            echo "Added permissions for user {$user['id']} ({$user['role']})\n";
        }
        
        echo "Sample data added successfully!\n";
    }
    
    echo "\nOperation completed successfully.\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
