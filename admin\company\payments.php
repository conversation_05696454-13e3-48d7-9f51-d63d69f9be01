<?php
/**
 * Company Payment Management
 * Company Admin interface for managing payments within their company
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and company admin privileges
requireLogin();
requireAdminAccess('company');

$database = new Database();
$db = $database->getConnection();

// Get current user's company ID
$company_id = $_SESSION['company_id'];

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$user_filter = $_GET['user'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 25;
$offset = ($page - 1) * $per_page;

// Build query for company payments only
$query = "SELECT p.*, u.name as user_name, u.email as user_email
          FROM payments p 
          LEFT JOIN users u ON p.user_id = u.id 
          WHERE p.company_id = ?";

$params = [$company_id];

if ($status_filter) {
    $query .= " AND p.payment_status = ?";
    $params[] = $status_filter;
}

if ($user_filter) {
    $query .= " AND p.user_id = ?";
    $params[] = $user_filter;
}

if ($date_from) {
    $query .= " AND DATE(p.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $query .= " AND DATE(p.created_at) <= ?";
    $params[] = $date_to;
}

// Count total records
$count_query = str_replace("SELECT p.*, u.name as user_name, u.email as user_email", "SELECT COUNT(*)", $query);
$count_stmt = $db->prepare($count_query);
$count_stmt->execute($params);
$total_records = $count_stmt->fetchColumn();
$total_pages = ceil($total_records / $per_page);

// Get paginated results
$query .= " ORDER BY p.created_at DESC LIMIT ? OFFSET ?";
$params[] = $per_page;
$params[] = $offset;

$stmt = $db->prepare($query);
$stmt->execute($params);
$payments = $stmt->fetchAll();

// Get company users for filter
$user_query = "SELECT id, name FROM users WHERE company_id = ? AND status = 'active' ORDER BY name";
$user_stmt = $db->prepare($user_query);
$user_stmt->execute([$company_id]);
$company_users = $user_stmt->fetchAll();

// Get payment statistics for this company
$stats_query = "SELECT
                    COUNT(*) as total_payments,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END), 0) as completed_amount,
                    COALESCE(SUM(CASE WHEN payment_status = 'pending' THEN amount ELSE 0 END), 0) as pending_amount,
                    COUNT(CASE WHEN payment_status = 'completed' THEN 1 END) as completed_count,
                    COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_count,
                    COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_count
                FROM payments WHERE company_id = ?";
$stats_stmt = $db->prepare($stats_query);
$stats_stmt->execute([$company_id]);
$stats = $stats_stmt->fetch();

// Get company info
$company_query = "SELECT company_name FROM companies WHERE id = ?";
$company_stmt = $db->prepare($company_query);
$company_stmt->execute([$company_id]);
$company_info = $company_stmt->fetch();

$page_title = "Company Payments";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('company'); ?>
    <style>
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .filter-label {
            font-weight: 600;
            color: #2B5E5F;
            font-size: 0.9rem;
        }
        
        .filter-input, .filter-select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }
        
        .payments-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .payments-table th {
            background: #2B5E5F;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .payments-table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: top;
        }
        
        .payments-table tr:hover {
            background: #f8fafa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .amount {
            font-weight: 700;
            font-size: 1.1rem;
        }
        
        .amount.positive {
            color: #065f46;
        }
        
        .amount.negative {
            color: #991b1b;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .pagination a, .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            text-decoration: none;
            color: #374151;
        }
        
        .pagination a:hover {
            background: #f9fafb;
        }
        
        .pagination .current {
            background: #2B5E5F;
            color: white;
            border-color: #2B5E5F;
        }
        
        .chart-container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
            margin-bottom: 2rem;
        }
        
        .chart-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #2B5E5F;
            margin-bottom: 1rem;
        }
        
        .no-data {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'company', 'payments.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Company Payments</h1>
            <p class="page-subtitle">Payment history for <?php echo htmlspecialchars($company_info['company_name']); ?></p>
        </div>
        
        <!-- Payment Statistics -->
        <div class="admin-stats">
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['total_payments']); ?></div>
                <div class="stat-label">Total Payments</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">R<?php echo number_format($stats['total_amount'], 2); ?></div>
                <div class="stat-label">Total Amount</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['completed_count']); ?></div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['pending_count']); ?></div>
                <div class="stat-label">Pending</div>
            </div>
        </div>
        
        <div class="admin-card">
            <!-- Filters -->
            <form method="GET" class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Status</label>
                    <select name="status" class="filter-select">
                        <option value="">All Statuses</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">User</label>
                    <select name="user" class="filter-select">
                        <option value="">All Users</option>
                        <?php foreach ($company_users as $user): ?>
                            <option value="<?php echo $user['id']; ?>" <?php echo $user_filter == $user['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Date From</label>
                    <input type="date" name="date_from" class="filter-input" value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Date To</label>
                    <input type="date" name="date_to" class="filter-input" value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                
                <div class="filter-group">
                    <button type="submit" class="btn">Filter</button>
                    <a href="payments.php" class="btn btn-secondary">Clear</a>
                </div>
            </form>
            
            <!-- Payments Table -->
            <table class="payments-table">
                <thead>
                    <tr>
                        <th>Payment ID</th>
                        <th>User</th>
                        <th>Amount</th>
                        <th>Description</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($payments as $payment): ?>
                        <tr>
                            <td>
                                <code style="font-weight: 600;">#<?php echo str_pad($payment['id'], 6, '0', STR_PAD_LEFT); ?></code>
                            </td>
                            <td>
                                <?php if ($payment['user_name']): ?>
                                    <div style="font-weight: 600;"><?php echo htmlspecialchars($payment['user_name']); ?></div>
                                    <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($payment['user_email']); ?></div>
                                <?php else: ?>
                                    <span style="color: #999;">System</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="amount positive">R<?php echo number_format($payment['amount'], 2); ?></div>
                            </td>
                            <td>
                                <div style="max-width: 200px; word-wrap: break-word;">
                                    <?php echo htmlspecialchars($payment['description'] ?? 'Payment'); ?>
                                </div>
                                <?php if ($payment['payment_gateway']): ?>
                                    <div style="font-size: 0.85rem; color: #666;">
                                        via <?php echo htmlspecialchars($payment['payment_gateway']); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $payment['payment_status']; ?>">
                                    <?php echo ucfirst($payment['payment_status']); ?>
                                </span>
                            </td>
                            <td>
                                <div><?php echo date('M j, Y', strtotime($payment['created_at'])); ?></div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo date('H:i:s', strtotime($payment['created_at'])); ?></div>
                            </td>
                            <td>
                                <a href="payment_details.php?id=<?php echo $payment['id']; ?>" class="btn btn-sm">View Details</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if (empty($payments)): ?>
                <div class="no-data">
                    <p>No payments found for the selected criteria.</p>
                    <p style="margin-top: 1rem;">
                        <a href="../../payments/make_payment.php" class="btn">Make a Payment</a>
                    </p>
                </div>
            <?php endif; ?>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">Previous</a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <?php if ($i == $page): ?>
                            <span class="current"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">Next</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Payment Summary Chart -->
        <?php if (!empty($payments)): ?>
            <div class="chart-container">
                <h3 class="chart-title">Payment Summary</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="text-align: center; padding: 1rem; background: #f8fafa; border-radius: 8px;">
                        <div style="font-size: 1.5rem; font-weight: 700; color: #065f46;">
                            R<?php echo number_format($stats['completed_amount'], 2); ?>
                        </div>
                        <div style="color: #666; font-size: 0.9rem;">Completed Payments</div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: #f8fafa; border-radius: 8px;">
                        <div style="font-size: 1.5rem; font-weight: 700; color: #92400e;">
                            R<?php echo number_format($stats['pending_amount'], 2); ?>
                        </div>
                        <div style="color: #666; font-size: 0.9rem;">Pending Payments</div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: #f8fafa; border-radius: 8px;">
                        <div style="font-size: 1.5rem; font-weight: 700; color: #2B5E5F;">
                            <?php echo $stats['completed_count'] > 0 ? number_format(($stats['completed_count'] / $stats['total_payments']) * 100, 1) : 0; ?>%
                        </div>
                        <div style="color: #666; font-size: 0.9rem;">Success Rate</div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: #f8fafa; border-radius: 8px;">
                        <div style="font-size: 1.5rem; font-weight: 700; color: #2B5E5F;">
                            R<?php echo $stats['total_payments'] > 0 ? number_format($stats['total_amount'] / $stats['total_payments'], 2) : 0; ?>
                        </div>
                        <div style="color: #666; font-size: 0.9rem;">Average Payment</div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </main>
</body>
</html>
